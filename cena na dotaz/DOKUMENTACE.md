# Modul "Cena na dotaz" - Dokumentace realizace

## P<PERSON><PERSON>led modulu
Modul pro PrestaShop 8.2.0, který automaticky detekuje produkty s cenou 0 Kč a zobrazí místo ceny text "Cena na dotaz" a místo tlačítka "Přidat do košíku" tlačítko "Zjistit cenu". Po kliknutí se zobrazí popup formulář pro dotaz na cenu.

## Realizace v 5 krocích

### Krok 1: Základní struktura modulu
- Vytvoření hlavního souboru modulu `priceinquiry.php`
- Konfigurace modulu (config.xml, translations)
- Základní instalace/odinstalace s databázovou tabulkou pro dotazy
- Registrace hooků pro detekci produktů s cenou 0

**Soubory:**
- `priceinquiry.php` (hlavní třída modulu)
- `config.xml` (konfigurace modulu)
- `translations/cs.php` (české překlady)
- `sql/install.php` (SQL pro vytvoření tabulky dotazů)

### Krok 2: Frontend detekce a zobrazení
- Hook pro detekci produktů s cenou 0 Kč
- Skrytí původního tlačítka "Přidat do košíku"
- Zobrazení textu "Cena na dotaz" místo ceny
- Zobrazení tlačítka "Zjistit cenu"

**Soubory:**
- `views/templates/front/price_inquiry_button.tpl`
- `views/css/front.css`
- `views/js/front.js`

### Krok 3: Popup formulář a AJAX
- Vytvoření popup formuláře pro dotaz na cenu
- Automatické vyplnění údajů pro přihlášené uživatele
- AJAX odeslání formuláře
- Frontend controller pro zpracování dotazu

**Soubory:**
- `views/templates/front/inquiry_modal.tpl`
- `controllers/front/inquiry.php`
- Rozšíření `views/js/front.js`

### Krok 4: Backend administrace
- Konfigurace modulu (nastavení e-mailu administrátora)
- Seznam přijatých dotazů
- Možnost označit dotaz jako vyřízený

**Soubory:**
- Rozšíření `priceinquiry.php` (getContent metoda)
- `views/templates/admin/configure.tpl`
- `views/templates/admin/inquiries_list.tpl`
- `views/css/admin.css`

### Krok 5: E-mailové notifikace
- Šablona e-mailu pro administrátora
- Šablona potvrzovacího e-mailu pro zákazníka
- Odeslání e-mailů při novém dotazu

**Soubory:**
- `mails/cs/admin_inquiry_notification.html`
- `mails/cs/admin_inquiry_notification.txt`
- `mails/cs/customer_inquiry_confirmation.html`
- `mails/cs/customer_inquiry_confirmation.txt`

## Databázová struktura

### Tabulka: `ps_price_inquiry`
```sql
- id_inquiry (int, AUTO_INCREMENT, PRIMARY KEY)
- id_product (int, NOT NULL)
- id_product_attribute (int, DEFAULT 0)
- id_customer (int, DEFAULT 0)
- customer_name (varchar 255, NOT NULL)
- customer_email (varchar 255, NOT NULL)
- customer_phone (varchar 50)
- message (text)
- date_add (datetime, NOT NULL)
- status (enum: 'new', 'processed', DEFAULT 'new')
```

## Hooky které budeme používat
- `displayProductAdditionalInfo` - zobrazení tlačítka "Zjistit cenu"
- `displayProductPriceBlock` - skrytí/úprava zobrazení ceny
- `displayHeader` - přidání CSS a JS souborů

## Konfigurace modulu
- `PRICE_INQUIRY_ADMIN_EMAIL` - e-mail administrátora pro notifikace
- `PRICE_INQUIRY_ENABLED` - zapnutí/vypnutí modulu

## Kompatibilita
- PrestaShop 8.2.0+
- Responsive design
- Kompatibilní s výchozím tématem