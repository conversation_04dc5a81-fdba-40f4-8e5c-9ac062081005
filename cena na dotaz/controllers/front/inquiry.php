<?php
/**
 * Frontend controller pro zpracování dotazů na cenu
 * Zpracovává AJAX požadavky z popup formuláře
 */

class PriceInquiryInquiryModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $display_column_left = false;
    public $display_column_right = false;
    public $display_header = false;
    public $display_footer = false;

    /**
     * Inicializace controlleru
     */
    public function init()
    {
        parent::init();
        
        // Kontrola, zda je požadavek AJAX
        if (!Tools::isSubmit('ajax')) {
            Tools::redirect('index.php');
        }
    }

    /**
     * Zpracování POST požadavku
     */
    public function postProcess()
    {
        if (Tools::isSubmit('ajax') && Tools::getValue('ajax') == '1') {
            $this->processAjaxInquiry();
        }
    }

    /**
     * Zpracování AJAX dotazu na cenu
     */
    private function processAjaxInquiry()
    {
        $response = array(
            'success' => false,
            'message' => '',
            'errors' => array()
        );

        try {
            // Validace vstupních dat
            $validation = $this->validateInquiryData();
            if (!$validation['valid']) {
                $response['errors'] = $validation['errors'];
                $this->ajaxDie(json_encode($response));
            }

            // Získání dat z formuláře
            $inquiryData = $this->getInquiryData();
            
            // Uložení dotazu do databáze
            $inquiryId = $this->saveInquiry($inquiryData);
            
            if ($inquiryId) {
                // Odeslání e-mailových notifikací
                $this->sendNotifications($inquiryId, $inquiryData);
                
                $response['success'] = true;
                $response['message'] = $this->module->l('Váš dotaz byl úspěšně odeslán. Brzy se Vám ozveme s nabídkou.', 'inquiry');
                $response['inquiry_id'] = $inquiryId;
            } else {
                $response['message'] = $this->module->l('Došlo k chybě při ukládání dotazu. Zkuste to prosím znovu.', 'inquiry');
            }

        } catch (Exception $e) {
            $response['message'] = $this->module->l('Došlo k neočekávané chybě. Zkuste to prosím znovu.', 'inquiry');
            PrestaShopLogger::addLog('PriceInquiry Error: ' . $e->getMessage(), 3);
        }

        $this->ajaxDie(json_encode($response));
    }

    /**
     * Validace dat z formuláře
     */
    private function validateInquiryData()
    {
        $errors = array();
        
        // Povinná pole
        if (!Tools::getValue('customer_name') || trim(Tools::getValue('customer_name')) == '') {
            $errors[] = $this->module->l('Jméno a příjmení je povinné pole.', 'inquiry');
        }
        
        if (!Tools::getValue('customer_email') || !Validate::isEmail(Tools::getValue('customer_email'))) {
            $errors[] = $this->module->l('Zadejte platnou e-mailovou adresu.', 'inquiry');
        }
        
        if (!Tools::getValue('id_product') || !Validate::isUnsignedId(Tools::getValue('id_product'))) {
            $errors[] = $this->module->l('Neplatný produkt.', 'inquiry');
        }
        
        // GDPR souhlas
        if (!Tools::getValue('gdpr_consent')) {
            $errors[] = $this->module->l('Musíte souhlasit se zpracováním osobních údajů.', 'inquiry');
        }
        
        // Kontrola existence produktu
        $productId = (int)Tools::getValue('id_product');
        $product = new Product($productId, false, $this->context->language->id);
        if (!Validate::isLoadedObject($product)) {
            $errors[] = $this->module->l('Produkt nebyl nalezen.', 'inquiry');
        }
        
        // Volitelná validace telefonu
        $phone = Tools::getValue('customer_phone');
        if ($phone && !empty(trim($phone)) && !Validate::isPhoneNumber($phone)) {
            $errors[] = $this->module->l('Zadejte platné telefonní číslo.', 'inquiry');
        }
        
        // Validace množství
        $quantity = (int)Tools::getValue('quantity');
        if ($quantity < 1) {
            $errors[] = $this->module->l('Množství musí být alespoň 1.', 'inquiry');
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Získání a příprava dat z formuláře
     */
    private function getInquiryData()
    {
        return array(
            'id_product' => (int)Tools::getValue('id_product'),
            'product_name' => pSQL(Tools::getValue('product_name')),
            'product_reference' => pSQL(Tools::getValue('product_reference')),
            'customer_name' => pSQL(Tools::getValue('customer_name')),
            'customer_email' => pSQL(Tools::getValue('customer_email')),
            'customer_phone' => pSQL(Tools::getValue('customer_phone')),
            'message' => pSQL(Tools::getValue('message')),
            'id_customer' => $this->context->customer->isLogged() ? (int)$this->context->customer->id : 0,
            'date_add' => date('Y-m-d H:i:s'),
            'resolved' => 0
        );
    }

    /**
     * Uložení dotazu do databáze
     */
    private function saveInquiry($data)
    {
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'price_inquiry` (
            `id_product`, `product_name`, `product_reference`,
            `customer_name`, `customer_email`, `customer_phone`,
            `message`, `id_customer`, `date_add`, `resolved`
        ) VALUES (
            ' . (int)$data['id_product'] . ',
            "' . $data['product_name'] . '",
            "' . $data['product_reference'] . '",
            "' . $data['customer_name'] . '",
            "' . $data['customer_email'] . '",
            "' . $data['customer_phone'] . '",
            "' . $data['message'] . '",
            ' . (int)$data['id_customer'] . ',
            "' . $data['date_add'] . '",
            ' . (int)$data['resolved'] . '
        )';

        if (Db::getInstance()->execute($sql)) {
            return Db::getInstance()->Insert_ID();
        }

        return false;
    }

    /**
     * Odeslání e-mailových notifikací
     */
    private function sendNotifications($inquiryId, $data)
    {
        // TODO: Implementace e-mailových notifikací v kroku 5
        // Zatím jen log pro testování
        PrestaShopLogger::addLog('PriceInquiry: New inquiry #' . $inquiryId . ' from ' . $data['customer_email'], 1);
    }

    /**
     * Zobrazení stránky (pro non-AJAX požadavky)
     */
    public function initContent()
    {
        // Přesměrování na hlavní stránku pro non-AJAX požadavky
        Tools::redirect('index.php');
    }
}
