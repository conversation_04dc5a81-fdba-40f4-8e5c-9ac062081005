<?php
/**
* 2024 Anvanto
*
* NOTICE OF LICENSE
*
* This source file is subject to the Academic Free License (AFL 3.0)
*
*  <AUTHOR> <<EMAIL>>
*  @copyright 2024 Anvanto
*  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
*/

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsValues.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anPfs.php';
require_once _PS_MODULE_DIR_ . 'an_productfields/classes/anProductFieldsGroups.php';

class anProductFields extends ObjectModel
{
    /**
     * @var int
     */
    public $id_field;
    /**
     * @var int
     */
    public $id;
    /**
     * @var int
     */
    public $active = 1;
    public $price = 0;
    public $type_price = 0;
    public $type_show_price;
    public $price_percent = 0;
    public $free_characters = 0;
    public $ignore_spaces = 0;
    public $custom_tax = 0;
    public $apply_specific_price = 0;
    public $relation = 0;
    public $not_global = 0;
    public $note;
    public $type = 'text';
    public $num_min = 1;
    public $num_max = 100;
    public $num_multiply_price = 0;
    public $id_default_value = 0;
    public $field_max_file_size = 2048;
    public $allow_files_format = '';
    public $required = 0;
    public $validation = '0';
    public $max_length = 0;
    public $char_counter = 0;
    public $position = 0;
    
    public $title;
    public $placeholder;
    public $descr;
    public $tooltip;

    public $file_display = 'miniature_left';
    public $file;
    public $file_text_link;


    /**
     * @var array
     */
    public static $definition = [
        'table' => 'an_productfields_fields',
        'primary' => 'id_field',
        'multilang' => true,
        'fields' => [
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            
            'price' => array('type' => self::TYPE_FLOAT),
            'custom_tax' => array('type' =>self::TYPE_INT ),
            'type_price' => array('type' =>self::TYPE_INT ),
            'type_show_price' => array('type' =>self::TYPE_STRING ),
            'price_percent' => array('type' =>self::TYPE_INT ),
            'apply_specific_price' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
    
            'free_characters' => array('type' =>self::TYPE_INT ),
            'ignore_spaces' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],

            'relation' => array('type' =>self::TYPE_INT ),
            'not_global' => array('type' => self::TYPE_BOOL, 'validate' => 'isBool'),
            
            'note' => ['type' =>self::TYPE_STRING ],
            'type' => ['type' =>self::TYPE_STRING ],
            'num_min' => ['type' =>self::TYPE_INT ],
            'num_max' => ['type' =>self::TYPE_INT ],            
            'num_multiply_price' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],            
            'id_default_value' => ['type' =>self::TYPE_INT ],
            'field_max_file_size' => array('type' =>self::TYPE_INT ),
            'allow_files_format' => ['type' =>self::TYPE_STRING ],
            'required' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'validation' => ['type' =>self::TYPE_STRING ],
            'max_length' => ['type' =>self::TYPE_INT ],
            'char_counter' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'position' => ['type' =>self::TYPE_INT ],
            
            'title' => ['type' =>self::TYPE_STRING,'lang' => true, 'validate' => 'isString', 'required' => true ],
            'placeholder' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'descr' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'placeholder' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'tooltip' => ['type' =>self::TYPE_STRING,'lang' => true ],

            'file_display' => ['type' =>self::TYPE_STRING ],
            'file' => ['type' =>self::TYPE_STRING,'lang' => true ],
            'file_text_link' => ['type' =>self::TYPE_STRING,'lang' => true ],

        ],
    ];

    const imgDir = _PS_MODULE_DIR_ . 'an_productfields/img/';
    const imgUrl = __PS_BASE_URI__ . 'modules/an_productfields/img/';

    public static $typesFiles = [
        'gif' => true, 
        'jpg' => true, 
        'jpeg' => true, 
        'jpe' => true, 
        'png' => true, 
        'webp' => true, 
        'svg' => true, 
        'pdf' => false
    ];

    public static $typesFields = [
        'text' => [
            'id' => 'text', 
            'name' => 'Text', 
            'values' => false
        ],
        'number' => [
            'id' => 'number', 
            'name' => 'Number', 
            'values' => false
        ],
        'textarea' => [
            'id' => 'textarea', 
            'name' => 'Textarea', 
            'values' => false
        ], 
        'date' => [
            'id' => 'date', 
            'name' => 'Date', 
            'values' => false
        ],
        'radio' => [
            'id' => 'radio', 
            'name' => 'Radiobutton', 
            'values' => true
        ], 
        'select'=> [
            'id' => 'select', 
            'name' => 'Select', 
            'values' => true
        ],
        'multiselect' => [
            'id' => 'multiselect', 
            'name' => 'Multi select', 
            'values' => true,
            'multi' => true,
        ],
        'checkbox' => [
            'id' => 'checkbox', 
            'name' => 'Checkbox', 
            'values' => true,
            'multi' => true,
        ],
        'range_slider'=> [
            'id' => 'range_slider', 
            'name' => 'Range slider', 
            'values' => true
        ],      
        'file'=> [
            'id' => 'file', 
            'name' => 'File', 
            'values' => false
        ]          
    ];

    /**
     * Formula constructor.
     *
     * @param null $id
     */

    public function __construct($id = null, $id_lang = null, $short = false)
    {
        parent::__construct($id, $id_lang);
        
        $this->isset_values = self::$typesFields[$this->type]['values'];
        $this->multi = false;
        if (isset(self::$typesFields[$this->type]['multi']) && self::$typesFields[$this->type]['multi']){
            $this->multi = true;
        }
        
        if (!$short){
            $this->values = [];
            if (self::$typesFields[$this->type]['values']){
                $this->values = anProductFieldsValues::getValuesByIdField($this->id);
            }
        }
        
        $this->price_wt = 0;

        $this->priceFormatted = '';
        if (!$this->isset_values && $this->price > 0){
            $this->priceFormatted = Tools::displayPrice($this->price);
        }

    }

    public static function getTypesFileds()
    {
        //  'name' => Context::getContext()->getTranslator()->trans('Text', [], 'Modules.an_productfields.Admin'), 
        //  сделать, чтобы через функцию эту возращался массив с переводами
        //  echo '<pre>'; var_dump(self::$typesFields); die;

        if (!Configuration::get('an_pf_type_field_file')){
            unset(self::$typesFields['file']);
        }

        return self::$typesFields;
     }

    public static function getAllFields()
    {
        $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields` sw';

        // if (Shop::isFeatureActive()) {
        //     $sql .= ' WHERE sw.`id_field` IN (
        //         SELECT sa.`id_field`
        //         FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sa
        //         WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
        //     )';
        // }

        $fields = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
       
        foreach ($fields as $key => $field){
            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` WHERE `id_field` = ' . (int) $field['id_field'] . '';
            $res = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            $fields[$key]['relations'] = $res;

            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` WHERE `id_field` = ' . (int) $field['id_field'] . '';
            $res = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            $fields[$key]['shops'] = $res;

            $sql_lang = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_lang` WHERE `id_field` = ' . (int) $field['id_field'] . '';
            $res_lang = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql_lang);
            $langContent = [];
            foreach ($res_lang as $item){
                $item['iso_code'] = Language::getIsoById($item['id_lang']);
                $langContent[$item['iso_code']] = $item;
            }            
            $fields[$key]['languages'] = $langContent;

            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values` WHERE `id_field` = ' . (int) $field['id_field'] . '';
            $res_values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            $fields[$key]['values'] = $res_values;

            $val = anProductFieldsValues::getAllValues($field['id_field']);
            
            $fields[$key]['values'] = $val;

        }

        return $fields;
    }

    public static function importJsonAllFields($data)
    {
        if (!($data)){
            return false;
        }

        $listLang = Language::getLanguages();

        try {

            foreach ($data as $item){
                    
                $fieldObj = new anProductFields();
                
                $fieldObj->active = $item['active'];

                if (!isset($item['type_price'])){
                    $item['type_price'] = 0;
                }
                $fieldObj->type_price = $item['type_price'];
                
                $fieldObj->price = $item['price'];

                if (!isset($item['price_percent'])){
                    $item['price_percent'] = 0;
                }

                if (!isset($item['ignore_spaces'])){
                    $item['ignore_spaces'] = 0;
                }

                if (!isset($item['free_characters'])){
                    $item['free_characters'] = 0;
                }            

                $fieldObj->price_percent = $item['price_percent'];
                
                $fieldObj->custom_tax = $item['custom_tax'];
                $fieldObj->relation = $item['relation'];      
                $fieldObj->note = $item['note'];
                if (!isset($item['id_default_value'])){
                    $item['id_default_value'] = 0;
                }
                $fieldObj->id_default_value = $item['id_default_value'];
                $fieldObj->type = $item['type'];


                if (!isset($item['num_min'])){ $item['num_min'] = 1; }
                $fieldObj->num_min = $item['num_min'];
                
                if (!isset($item['num_max'])){ $item['num_max'] = 100; }
                $fieldObj->num_max = $item['num_max'];

                if (!isset($item['num_multiply_price'])){ $item['num_multiply_price'] = 0; }
                $fieldObj->num_multiply_price = $item['num_multiply_price'];



                $fieldObj->required = $item['required'];
                $fieldObj->validation = $item['validation'];
                $fieldObj->max_length = $item['max_length'];
                if (!isset($item['char_counter'])){
                    $item['char_counter'] = 0;
                }            
                $fieldObj->char_counter = $item['char_counter'];
                $fieldObj->position = $item['position'];

                $firstLangId = '';
                foreach ($item['languages'] as $key => $field) {
                    
                    if (isset($field['iso_code']) && $field['iso_code']){
                        $langId = Language::getIdByIso($field['iso_code']);
                    } else {
                        $langId = $field['id_lang'];
                    }

                    if (trim($field['title']) == ''){
                        $field['title'] = '-';
                    }

                    $fieldObj->title[$langId] = $field['title'];
                    $fieldObj->placeholder[$langId] = $field['placeholder'];
                    $fieldObj->descr[$langId] = $field['descr'];
                    $fieldObj->tooltip[$langId] = $field['tooltip'];

                    if ($firstLangId == ''){
                        $firstLangId = $langId;
                    }
                }

                foreach ($listLang as $lg){
                    if (!isset($fieldObj->title[$lg['id_lang']])){
                        $fieldObj->title[$lg['id_lang']] = $fieldObj->title[$firstLangId];
                        $fieldObj->placeholder[$lg['id_lang']] = $fieldObj->placeholder[$firstLangId];
                        $fieldObj->descr[$lg['id_lang']] =  $fieldObj->descr[$firstLangId];
                        $fieldObj->tooltip[$lg['id_lang']] =   $fieldObj->tooltip[$firstLangId];
                    }
                }

                $fieldObj->save();
                
                foreach ($item['values'] as $value){
                    $valuesObj = new anProductFieldsValues();
                    
                    $valuesObj->id_field = $fieldObj->id;
                    if (!isset($value['type_price'])){
                        $value['type_price'] = 0;
                    }
                    $valuesObj->type_price = $value['type_price'];

                    $valuesObj->price = $value['price'];
                
                    if (!isset($value['price_percent'])){
                        $value['price_percent'] = 0;
                    }
                    $valuesObj->price_percent = $value['price_percent'];
                    
                    $valuesObj->custom_tax = $value['custom_tax'];
                    $valuesObj->position = $value['position'];
                    
                    if (!isset($value['file_display'])){
                        $value['file_display'] = 'miniature_left';
                    }
                    $valuesObj->file_display = $value['file_display'];

                    $valuesObj->active = $value['active'];
                    
                    foreach ($value['languages_values'] as $key => $valueLang) {

                        if (isset($field['iso_code']) && $field['iso_code']){
                            $langId = Language::getIdByIso($valueLang['iso_code']);
                        } else {
                            $langId = $valueLang['id_lang'];
                        }

                        if (trim($valueLang['title']) == ''){
                            $valueLang['title'] = '-';
                        }

                        $valuesObj->title[$langId] = $valueLang['title'];
                        $valuesObj->file[$langId] = $valueLang['file'];
                    }

                    $valuesObj->save();
            
                    if (isset($value['shops'])){
                        foreach ($value['shops'] as $key => $shopItemValue){
                            $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_values_shop`  (`id_value`, `id_shop`) 
                            VALUES ("'.(int) $valuesObj->id.'", "'.(int) $shopItemValue['id_shop'].'" )';
                            Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
                        }
                    }                 
                }

                foreach ($item['relations'] as $key => $field){
                    $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_relations`  (`id_field`, `id_type`, `type`) 
                    VALUES ("'.(int) $fieldObj->id.'","'.(int) $field['id_type'].'","'.(int) $field['type'].'" )';
                    Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
                } 

                if (isset($item['shops'])){
                    foreach ($item['shops'] as $key => $shopItemField){
                        $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_shop`  (`id_field`, `id_shop`) 
                        VALUES ("'.(int) $fieldObj->id.'","'.(int) $shopItemField['id_shop'].'" )';
                        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
                    } 
                }
            }
        } catch (Exception $e) {
            return false;       
        }
    
        return true; 
    }   

    public static function getValidationsList()
    {
        return [
            ['id' => 'isCarrierName', 'name' => 'Alphanumeric'], 
            ['id' => 'isFloat', 'name' => 'Numeric'],
            ['id' => 'isName', 'name' => 'Alphabetic'], 
            ['id' => 'isUrl', 'name' => 'URL'],
            ['id' => 'isEmail', 'name' => 'Email'],
        ];
    }

    public static function issetFieldsForProduct($id_product, $required = true)
    {
        if (!$id_product) {
            return false;
        }

        $cats = product::getProductCategories($id_product);

		$sql = '
		SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` szwr, `' . _DB_PREFIX_ . 'an_productfields_fields` sw
		WHERE sw.`active`=1
			AND sw.`id_field` = szwr.`id_field` AND sw.`relation` = szwr.`type`
			AND ((szwr.`type` = 1 AND szwr.`id_type` IN (' . implode(', ', $cats) . ')   )
				OR (szwr.`type` = 2 AND szwr.`id_type` = '.(int) $id_product.')
				OR (szwr.`type` = 0 AND szwr.`id_type` = 0)
				)
		';	

        if ($required){
            $sql .= 'AND sw.`required` = 1';
        }
	
		if (Shop::isFeatureActive()) {
			$sql .= ' AND sw.`id_field` IN (
				SELECT sa.`id_field`
				FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sa
				WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
			)';
		}

        return (bool) Db::getInstance()->getValue($sql);
    }

    public static function getFieldsByIdProduct($id_product, $id_product_attribute = null, $prices = [], $notGlobal = false, $paramsProduct = [])
    {
        $context = Context::getContext();

        $specificPricesReduction = 0;
        if (isset($paramsProduct->specific_prices) && isset($paramsProduct->specific_prices['reduction_type']) && 
            $paramsProduct->specific_prices['reduction_type'] == 'percentage' && 
            $paramsProduct->specific_prices['id_product_attribute'] == 0)
            {
            $specificPricesReduction = $paramsProduct->specific_prices['reduction'];
        }
        
        $id_currency = Configuration::get('PS_CURRENCY_DEFAULT');
        if (isset($context->cart->id_currency) && $context->cart->id_currency){
            $id_currency = $context->cart->id_currency;
        }
        $currency = new Currency($id_currency);        
        
        $cats = [];
        if (!Configuration::get('an_pf_onlyCategoryDefault')){
            $cats = Product::getProductCategories($id_product);
        } else {
            $product = new Product($id_product);
            $cats[] = $product->id_category_default;
        }

        $fieldGroups = anProductFieldsGroups::getGroupsByIdProduct($id_product, $cats);
        $fieldGroupsIm = [];
        if ($fieldGroups){
            foreach ($fieldGroups as $fg){
                $fieldGroupsIm[] = $fg['id_group'];
            }
        }

        $notGlobalFieldIds = [];
        $resultNotGlobalFieldIds = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS('
        SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_products` 
        WHERE `id_product` = ' . (int) $id_product . ' AND `active` = 1 
        ');
        foreach ($resultNotGlobalFieldIds as $idField){
            $notGlobalFieldIds[] = $idField['id_field'];
        }

        $sqlNotGlobal = '';
        if ($notGlobal) {
            $sqlNotGlobal = 'AND sw.`not_global` = 1 ';
        } elseif (count($notGlobalFieldIds) > 0) {
            $sqlNotGlobal = 'AND (sw.`not_global` = 0 OR (sw.`not_global` = 1 AND sw.`id_field` IN (' . implode(', ', $notGlobalFieldIds) . ')))';
        } else {
            $sqlNotGlobal = 'AND (sw.`not_global` = 0)';
        }


		$sql = '
            SELECT * FROM ';

        if (Configuration::get('an_pf_customer_groups')){
            $sql .= '`' . _DB_PREFIX_ . 'an_productfields_customer_groups` apfcg, ';
        }

        $sql .= '        `' . _DB_PREFIX_ . 'an_productfields_fields_relations` szwr, 
                `' . _DB_PREFIX_ . 'an_productfields_fields` sw
            LEFT JOIN `' . _DB_PREFIX_ . 'an_productfields_fields_lang` sl 
                ON (sw.`id_field` = sl.`id_field`
                AND sl.`id_lang` = ' . (int) $context->language->id . ')
            WHERE sw.`active`=1';
            
        if (Configuration::get('an_pf_customer_groups')){
            $sql .= '    AND apfcg.`id_group` = '.(int)$context->customer->id_default_group.' AND apfcg.`id_data` = sw.`id_field` AND apfcg.`type` = 0';
        }
            
            $sql .= '    AND sw.`id_field` = szwr.`id_field` 
                ' . $sqlNotGlobal . '
                
                AND ';
                    $sql .= '(sw.`relation` = szwr.`type` AND ';
                        $sql .= '(';
                            if (count($cats) > 0){
                                $sql .= '(szwr.`type` = 1 AND szwr.`id_type` IN (' . implode(', ', $cats) . ')   ) OR ';
                            }
                            if (count($fieldGroupsIm) > 0){
                                $sql .= '(szwr.`type` = 3 AND szwr.`id_type` IN (' . implode(', ', $fieldGroupsIm) . ')   ) OR ';
                            }                            
                            $sql .= '(szwr.`type` = 2 AND szwr.`id_type` = '.(int) $id_product.') OR'; 
                            $sql .= '(szwr.`type` = 4 AND szwr.`id_type` = '.(int) $id_product.' AND szwr.`id_product_attribute` = '.(int) $id_product_attribute.') OR'; 
                            $sql .= '(szwr.`type` = 0 AND szwr.`id_type` = 0)'; 

                           
                        $sql .= ')';
                    $sql .= ')';
        
	
		if (Shop::isFeatureActive()) {
			$sql .= ' AND sw.`id_field` IN (
				SELECT sa.`id_field`
				FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sa
				WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
			)';
		}
       
        $sql .= ' GROUP BY sw.`id_field`';
        $sql .= ' ORDER BY sw.`position`';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $fields = [];
        foreach ($result as $id => $field){

            $field['isset_values'] = self::$typesFields[$field['type']]['values'];
            $field['multi'] = false;
            if (isset(self::$typesFields[$field['type']]['multi']) && self::$typesFields[$field['type']]['multi']){
                $field['multi'] = true;
            }

            $field['values'] = [];
            if (self::$typesFields[$field['type']]['values']){
                $field['values'] = anProductFieldsValues::getValuesByIdField($field['id_field'], $id_product, $prices, $specificPricesReduction);
            }

            $field['price_wt'] = 0;
            $field['priceFormatted'] = '';
            if (
                !$field['isset_values'] && $field['price'] != 0 && 
                (!isset($field['type_price']) || $field['type_price'] == 0 || $field['type_price'] == 2) 
                ){

                $field['price'] = anPfs::applyReductionCustomerGroup($field['price'], $id_product);
                if ($specificPricesReduction && $field['apply_specific_price']){
                    $field['price'] -= $field['price'] * $specificPricesReduction;
                    $field['price_without_reduction'] = $field['price'];
                    $field['reduction_percentage'] = $specificPricesReduction * 100;
                }

                $field['price'] = Tools::convertPriceFull($field['price'], null, $currency);
                $field['price_wt'] = anProductFieldsTax::addTax($field['price'], $field['custom_tax'], $id_product);

                $field['priceFormatted'] = anPfs::formatPrice($field['price'], $field['price_wt']);
            }   
            
            if (!$field['isset_values'] && $field['price_percent'] != 0 && (isset($field['type_price']) && $field['type_price'] == 1)){
                $field['price'] = $field['price_wt'] = $field['price_percent'];
                $field['priceFormatted'] = $field['price_percent'] . '%';
            }

            $field['priceImpact'] = 'no';
            if ($field['price'] < 0){
                $field['priceImpact'] = 'minus';
            } else if ($field['price'] > 0){
                $field['priceImpact'] = 'plus';
            }


            $field['fileUrl'] = '';
            $fiel['isFileImg'] = false;
            if ($field['file'] != '' ){
                $field['fileUrl'] = self::imgUrl . $field['file'];
                $extension = pathinfo(trim(strip_tags($field['file'])), PATHINFO_EXTENSION);
                $extension = Tools::strtolower($extension);
                $field['format'] = $extension;
                $field['isFileImg'] = self::$typesFiles[$field['format']];
            }            


            $fields[$field['id_field']] = $field;
        }

        return $fields;
    }

    public static function getFieldsByIdProductForForm($idProduct)
    {
        $fields = anProductFields::getFieldsByIdProduct($idProduct, null, [], true);

        foreach ($fields as &$field){
            $field['notGlobalActive'] = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue('
            SELECT `active` FROM `' . _DB_PREFIX_ . 'an_productfields_fields_products` 
            WHERE `id_product` = ' . (int) $idProduct . ' AND `id_field` = ' . (int) $field['id_field'] . '
            ');    
        }

        return $fields;
    }

	public static function getProducsByIdField($id_field = 0)
	{
		if (!$id_field){
			return [];
		}
		
		$sql = '
		SELECT  *, p.*
		FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` awl
		
        LEFT JOIN `' . _DB_PREFIX_ . 'product` p
            ON (p.`id_product` = awl.`id_type`)
		
		LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl
            ON (p.`id_product` = pl.`id_product`
            AND pl.`id_lang` = ' . (int) Context::getContext()->language->id . Shop::addSqlRestrictionOnLang('pl') . ')		
		
		WHERE awl.`id_field` = ' . (int) $id_field . '  AND awl.`type`="2" ';
		
		
		$result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql, true, false);

		$products = Product::getProductsProperties(Context::getContext()->language->id, $result);

        foreach ($products as &$product){

            $coverPhoto = '';
            if (isset($product['cover_image_id'])){
                $coverPhoto = Context::getContext()->link->getImageLink(
                    $product['link_rewrite'],
                    $product['cover_image_id'], 
                    ImageType::getFormattedName('cart')
                );
            }
            
            $product['cover'] = $coverPhoto;
        }

        return $products;
	} 

	public static function getRelationCategories($id_field = 0)
	{
		if (!$id_field){
			return [];
		}
		
		$sql = '
		SELECT `id_type`
		FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` awl		
		WHERE awl.`id_field` = ' . (int) $id_field . ' AND awl.`type`="1"  ';
		
		$result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql, true, false);
		
		$cats = [];
		if ($result) {
			foreach ($result as $item){
				$cats[] = $item['id_type'];
			}
		}
		
		return $cats;		
	}  



    public static function getRelationsByIdField($idField)
    {
        $sql = '
		SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` szwr
		WHERE `id_field` = '.(int) $idField.'';	
        if (Shop::isFeatureActive()) {
			$sql .= ' AND szwr.`id_field` IN (
				SELECT sa.`id_field`
				FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sa
				WHERE sa.id_shop IN (' . implode(', ', Shop::getContextListShopID()) . ')
			)';
		}
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }
    

    public function setCustomerGroups($customer_groups = [])
    {
        Db::getInstance(_PS_USE_SQL_SLAVE_)->execute('
        DELETE FROM `' . _DB_PREFIX_ . 'an_productfields_customer_groups` WHERE `type` = 0 AND `id_data` = ' . (int) $this->id);

        if (!$customer_groups) {
            return false;
        }

        foreach ($customer_groups as $id){
            $sql = '
            INSERT INTO `'._DB_PREFIX_.'an_productfields_customer_groups`  (`id_group`, `id_data`, `type`) 
            VALUES ("'.(int) $id.'", "'.$this->id.'", 0 )';
            Db::getInstance(_PS_USE_SQL_SLAVE_)->execute($sql);
        }
        
        
        return true;
    }

    public function getCustomerGroups()
    {
        $sql = '
		SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_customer_groups` 
		WHERE `type` = 0 AND `id_data` = '.(int) $this->id.'';	
        $rowsGroups = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

        $groups = [];
        foreach ($rowsGroups as $item){
            $groups[$item['id_group']] = $item['id_group'];
        }

        return $groups;
    }

    public function getAllowFilesFormat()
    {
        $allowFilesFormat = json_decode($this->allow_files_format);

        $formats = [];
        if (is_array($allowFilesFormat)){
            foreach ($allowFilesFormat as $format){
                $formats[$format] = $format;
            }
        }

        return $formats;
    }

    public function prepareSomeFields()
    {
        if ($this->type_price != 1){
            $this->type_show_price = '';
        }  

        if ($this->type != 'number'){
            $this->num_multiply_price = 0;
        }  

        $allow_files_format = Tools::getValue('allow_files_format_array');
        if (is_array($allow_files_format)){
            $this->allow_files_format = json_encode($allow_files_format);
        }

        if ($this->type != 'file'){
            $this->allow_files_format = '';
        }
    }

    public function save($null_values = false, $auto_date = true)
    {        
        $this->prepareSomeFields();
        return parent::save($null_values, $auto_date);
    } 

    public function add($auto_date = true, $null_values = false)
    {   
        $this->prepareSomeFields();
        return parent::add($null_values, $null_values);
    }

    public function update($null_values = false)
    {   
        $this->prepareSomeFields();
        return parent::update($null_values);
    }

    public function delete()
    {        
        foreach ($this->file as $fileName){
            @unlink(self::imgDir . $fileName);
        }

        $values = anProductFieldsValues::getValuesByIdField($this->id);
        foreach ($values as $value){
            $valObj = new anProductFieldsValues($value['id_value']);
            $valObj->delete();
        }

        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('DELETE FROM `'._DB_PREFIX_.'an_productfields_fields_relations` WHERE `id_field`='.(int) $this->id.' ');        
        Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute('
            DELETE FROM `'._DB_PREFIX_.'an_productfields_customer_groups` WHERE `type` = 0 AND `id_data`='.(int) $this->id.' 
        ');        

        anPfsDependencies::deleteAllByIdField($this->id);
        

        return parent::delete();
    }  


    public static function duplicate($idField)
    {
        $obj = new anProductFields($idField);
        $newObj = $obj->duplicateObject();

        if ($newObj){
            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_values` sw WHERE sw.`id_field` = ' . (int) $idField . '';
            $values = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);

            foreach ($values as $value){
                $newObjValue = new anProductFieldsValues($value['id_value']);
                $newObjValue->duplicateObject();
                $newObjValue->id_field = $newObj->id;
                $newObjValue->save();
            }

            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_relations` sw WHERE sw.`id_field` = ' . (int) $idField . '';
            $relations = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            foreach ($relations as $relation){
                $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_relations`  (`id_field`, `id_type`, `type`) 
                VALUES ("'.(int) $newObj->id.'", "'.(int) $relation['id_type'].'", "'.(int) $relation['type'].'" )';
                Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
            }

            $sql = 'SELECT * FROM `' . _DB_PREFIX_ . 'an_productfields_fields_shop` sw WHERE sw.`id_field` = ' . (int) $idField . '';
            $shops = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
            foreach ($shops as $shop){
                $sql = 'INSERT INTO `'._DB_PREFIX_.'an_productfields_fields_shop`  (`id_field`, `id_shop`) 
                VALUES ("'.(int) $newObj->id.'", "'.(int) $shop['id_shop'].'" )';
                Db::getInstance(_PS_USE_SQL_SLAVE_)->Execute($sql);
            }            
        }
    }

    public function duplicateObject()
    {
        $newObj = parent::duplicateObject();
        
        $languages = Language::getLanguages(false);
        foreach ($languages as $lang) {
            $newObj->title[$lang['id_lang']] = 'copy of ' . $newObj->title[$lang['id_lang']];
        }

        $newObj->save();

        return $newObj;
    }
}