/**
 * Order Detail JavaScript
 * CIG Realizace - Phase 05
 */

$(document).ready(function() {
    // Initialize order detail functionality
    initOrderDetail();
});

function initOrderDetail() {
    // Status form submission
    $('#preview-status-form').on('submit', handleStatusUpdate);
    
    // Technology assignment
    $('.technology-select').on('change', handleTechnologyChange);
    $('.technology-save-btn').on('click', handleTechnologySave);
    
    // Item relevance toggle
    $('.relevance-toggle').on('click', handleRelevanceToggle);
    
    // Item editing
    $('.item-edit-btn').on('click', handleItemEdit);
    $('.item-save-btn').on('click', handleItemSave);
    $('.item-cancel-btn').on('click', handleItemCancel);
    
    // Inventory status update
    $('.inventory-select').on('change', handleInventoryStatusChange);
}

// Handle preview status update
function handleStatusUpdate(e) {
    e.preventDefault();
    
    const form = $(this);
    const formData = form.serialize();
    const submitBtn = form.find('button[type="submit"]');
    
    // Show loading state
    submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Ukládám...');
    
    $.ajax({
        url: '../ajax/update_preview_status.php',
        method: 'POST',
        data: formData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', response.message);
                
                // Update status badge
                updateStatusBadge(response.data.new_status);
                
                // Add to history
                addHistoryItem(response.data);
                
                // Close modal if open
                $('#editStatusModal').modal('hide');
                
                // Reload page after short delay to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při komunikaci se serverem');
            console.error('AJAX Error:', error);
        },
        complete: function() {
            submitBtn.prop('disabled', false).html('<i class="fas fa-save me-1"></i>Uložit změny');
        }
    });
}

// Handle technology assignment change
function handleTechnologyChange() {
    const select = $(this);
    const itemId = select.data('item-id');
    const technology = select.val();
    
    if (technology && itemId) {
        updateTechnology(itemId, technology);
    }
}

// Handle technology save button
function handleTechnologySave() {
    const btn = $(this);
    const itemId = btn.data('item-id');
    const input = btn.siblings('.technology-input');
    const technology = input.val().trim();
    
    if (itemId) {
        updateTechnology(itemId, technology);
    }
}

// Update technology assignment
function updateTechnology(itemId, technology) {
    $.ajax({
        url: '../ajax/update_technology.php',
        method: 'POST',
        data: {
            item_id: itemId,
            technology: technology
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', 'Technologie byla úspěšně přiřazena');
                
                // Update display
                const row = $(`tr[data-item-id="${itemId}"]`);
                row.find('.technology-display').text(technology || '-');
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při ukládání technologie');
            console.error('AJAX Error:', error);
        }
    });
}

// Handle relevance toggle
function handleRelevanceToggle() {
    const toggle = $(this);
    const itemId = toggle.data('item-id');
    const currentRelevance = toggle.hasClass('relevant');
    const newRelevance = !currentRelevance;
    
    $.ajax({
        url: '../ajax/toggle_item_relevance.php',
        method: 'POST',
        data: {
            item_id: itemId,
            is_relevant: newRelevance ? 1 : 0
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update toggle appearance
                if (newRelevance) {
                    toggle.removeClass('irrelevant').addClass('relevant')
                          .html('<i class="fas fa-check"></i>');
                } else {
                    toggle.removeClass('relevant').addClass('irrelevant')
                          .html('<i class="fas fa-times"></i>');
                }
                
                // Update row appearance
                const row = toggle.closest('tr');
                if (newRelevance) {
                    row.removeClass('text-muted');
                } else {
                    row.addClass('text-muted');
                }
                
                showNotification('success', response.message);
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při změně relevance položky');
            console.error('AJAX Error:', error);
        }
    });
}

// Handle inventory status change
function handleInventoryStatusChange() {
    const select = $(this);
    const itemId = select.data('item-id');
    const status = select.val();
    
    $.ajax({
        url: '../ajax/update_item_status.php',
        method: 'POST',
        data: {
            item_id: itemId,
            inventory_status: status
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', 'Stav zásob byl aktualizován');
                
                // Update status badge
                const row = select.closest('tr');
                const badge = row.find('.inventory-badge');
                badge.removeClass().addClass('badge inventory-' + status.replace('_', '-'));
                badge.text(response.status_label);
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při aktualizaci stavu zásob');
            console.error('AJAX Error:', error);
        }
    });
}

// Handle item editing
function handleItemEdit() {
    const btn = $(this);
    const row = btn.closest('tr');
    
    // Show edit controls
    row.find('.item-display').hide();
    row.find('.item-edit').show();
    
    // Toggle buttons
    btn.hide();
    row.find('.item-save-btn, .item-cancel-btn').show();
}

// Handle item save
function handleItemSave() {
    const btn = $(this);
    const row = btn.closest('tr');
    const itemId = row.data('item-id');
    
    // Collect form data
    const data = {
        item_id: itemId,
        catalog_code: row.find('.catalog-input').val(),
        quantity: row.find('.quantity-input').val(),
        technology_assignment: row.find('.technology-input').val()
    };
    
    // Save item data
    $.ajax({
        url: '../ajax/update_item.php',
        method: 'POST',
        data: data,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                // Update display values
                row.find('.catalog-display').text(data.catalog_code);
                row.find('.quantity-display').text(parseFloat(data.quantity).toFixed(4));
                row.find('.technology-display').text(data.technology_assignment || '-');
                
                // Hide edit controls
                row.find('.item-edit').hide();
                row.find('.item-display').show();
                
                // Toggle buttons
                row.find('.item-save-btn, .item-cancel-btn').hide();
                row.find('.item-edit-btn').show();
                
                showNotification('success', 'Položka byla úspěšně aktualizována');
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při ukládání položky');
            console.error('AJAX Error:', error);
        }
    });
}

// Handle item cancel
function handleItemCancel() {
    const btn = $(this);
    const row = btn.closest('tr');
    
    // Hide edit controls
    row.find('.item-edit').hide();
    row.find('.item-display').show();
    
    // Toggle buttons
    row.find('.item-save-btn, .item-cancel-btn').hide();
    row.find('.item-edit-btn').show();
    
    // Reset form values
    row.find('.item-edit input, .item-edit select').each(function() {
        const input = $(this);
        const originalValue = input.data('original-value');
        if (originalValue !== undefined) {
            input.val(originalValue);
        }
    });
}

// Update status badge
function updateStatusBadge(newStatus) {
    const statusLabels = {
        'not_created': 'Nevytvořen',
        'sent_to_client': 'Odeslán klientovi',
        'approved': 'Schválen'
    };
    
    const badge = $('.preview-status-badge');
    badge.removeClass().addClass('status-badge status-' + newStatus.replace('_', '-'));
    badge.text(statusLabels[newStatus] || newStatus);
}

// Add history item
function addHistoryItem(data) {
    const historyContainer = $('.history-timeline');
    const historyItem = $(`
        <div class="history-item fade-in">
            <div class="history-meta">
                <i class="fas fa-clock me-1"></i>Právě teď - ${getCurrentUser()}
            </div>
            <div class="history-description">
                Stav náhledu změněn z '${data.old_status}' na '${data.new_status}'
            </div>
        </div>
    `);
    
    historyContainer.prepend(historyItem);
}

// Show notification
function showNotification(type, message) {
    const notification = $(`
        <div class="notification ${type} fade-in">
            <div class="d-flex align-items-center p-3">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.notification').remove()"></button>
            </div>
        </div>
    `);
    
    $('body').append(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 5000);
}

// Get current user name (placeholder)
function getCurrentUser() {
    return 'Aktuální uživatel';
}

// Complete order function
function completeOrder(orderId) {
    if (!confirm('Opravdu chcete označit tuto objednávku jako dokončenou?')) {
        return;
    }

    // Prompt for optional notes
    const notes = prompt('Poznámka k dokončení objednávky (volitelné):');
    if (notes === null) {
        return; // User cancelled
    }

    $.ajax({
        url: '../ajax/complete_order.php',
        method: 'POST',
        data: {
            order_id: orderId,
            notes: notes
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', response.message);

                // Reload page after short delay to show updated data
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při označování objednávky jako dokončené');
            console.error('AJAX Error:', error);
        }
    });
}
