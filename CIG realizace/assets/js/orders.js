/**
 * Orders Management JavaScript
 * CIG Realizace - Phase 04
 */

// Global variable to track expanded orders
let expandedOrders = new Set();

document.addEventListener('DOMContentLoaded', function() {
    // Initialize orders functionality
    initializeOrdersTable();
    initializeFilters();
    initializeSorting();
    initializeSearch();
    initializeOrderExpansion();
});

/**
 * Initialize orders table functionality
 */
function initializeOrdersTable() {
    // Add hover effects to table rows
    const tableRows = document.querySelectorAll('.orders-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
    
    // Initialize action buttons
    initializeActionButtons();
}

/**
 * Initialize action buttons
 */
function initializeActionButtons() {
    // View order buttons
    const viewButtons = document.querySelectorAll('.action-btn[data-action="view"]');
    viewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.dataset.orderId;
            viewOrderDetail(orderId);
        });
    });
    
    // Edit order buttons
    const editButtons = document.querySelectorAll('.action-btn[data-action="edit"]');
    editButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.dataset.orderId;
            editOrder(orderId);
        });
    });
    
    // Status change buttons
    const statusButtons = document.querySelectorAll('.action-btn[data-action="status"]');
    statusButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const orderId = this.dataset.orderId;
            const currentStatus = this.dataset.currentStatus;
            changeOrderStatus(orderId, currentStatus);
        });
    });
}

/**
 * Initialize filters functionality
 */
function initializeFilters() {
    const filterForm = document.getElementById('filtersForm');
    if (!filterForm) return;
    
    // Auto-submit on filter change
    const filterInputs = filterForm.querySelectorAll('select, input[type="date"]');
    filterInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Small delay to allow multiple quick changes
            clearTimeout(this.filterTimeout);
            this.filterTimeout = setTimeout(() => {
                submitFilters();
            }, 300);
        });
    });
    
    // Clear filters button
    const clearButton = document.getElementById('clearFilters');
    if (clearButton) {
        clearButton.addEventListener('click', function(e) {
            e.preventDefault();
            clearAllFilters();
        });
    }
}

/**
 * Initialize search functionality
 */
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            submitFilters();
        }, 500); // Wait 500ms after user stops typing
    });
    
    // Clear search button
    const clearSearchButton = document.getElementById('clearSearch');
    if (clearSearchButton) {
        clearSearchButton.addEventListener('click', function() {
            searchInput.value = '';
            submitFilters();
        });
    }
}

/**
 * Initialize sorting functionality
 */
function initializeSorting() {
    const sortableHeaders = document.querySelectorAll('.sortable');
    sortableHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const column = this.dataset.column;
            const currentOrder = this.dataset.order || 'desc';
            const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
            
            // Update URL with new sort parameters
            const url = new URL(window.location);
            url.searchParams.set('sort', column);
            url.searchParams.set('order', newOrder);
            
            // Navigate to new URL
            window.location.href = url.toString();
        });
    });
}

/**
 * Submit filters form
 */
function submitFilters() {
    const filterForm = document.getElementById('filtersForm');
    if (!filterForm) return;
    
    // Show loading state
    showLoadingState();
    
    // Submit form
    filterForm.submit();
}

/**
 * Clear all filters
 */
function clearAllFilters() {
    const filterForm = document.getElementById('filtersForm');
    if (!filterForm) return;
    
    // Clear all form inputs
    const inputs = filterForm.querySelectorAll('select, input');
    inputs.forEach(input => {
        if (input.type === 'select-one') {
            input.selectedIndex = 0;
        } else {
            input.value = '';
        }
    });
    
    // Submit cleared form
    submitFilters();
}

/**
 * View order detail
 */
function viewOrderDetail(orderId) {
    window.location.href = `detail.php?id=${orderId}`;
}

/**
 * Edit order
 */
function editOrder(orderId) {
    window.location.href = `edit.php?id=${orderId}`;
}

/**
 * Change order status
 */
function changeOrderStatus(orderId, currentStatus) {
    // Create modal or inline form for status change
    const modal = createStatusChangeModal(orderId, currentStatus);
    document.body.appendChild(modal);
    
    // Show modal
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
    
    // Clean up when modal is hidden
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

/**
 * Create status change modal
 */
function createStatusChangeModal(orderId, currentStatus) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Změna stavu objednávky</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="statusChangeForm">
                        <input type="hidden" name="order_id" value="${orderId}">
                        <div class="mb-3">
                            <label for="preview_status" class="form-label">Stav náhledu:</label>
                            <select class="form-select" name="preview_status" id="preview_status">
                                <option value="not_created" ${currentStatus === 'not_created' ? 'selected' : ''}>Nevytvořen</option>
                                <option value="sent_to_client" ${currentStatus === 'sent_to_client' ? 'selected' : ''}>Odeslán klientovi</option>
                                <option value="approved" ${currentStatus === 'approved' ? 'selected' : ''}>Schválen</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="notes" class="form-label">Poznámka:</label>
                            <textarea class="form-control" name="notes" id="notes" rows="3" placeholder="Volitelná poznámka k změně..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zrušit</button>
                    <button type="button" class="btn btn-primary" onclick="submitStatusChange()">Uložit změnu</button>
                </div>
            </div>
        </div>
    `;
    
    return modal;
}

/**
 * Submit status change
 */
function submitStatusChange() {
    const form = document.getElementById('statusChangeForm');
    const formData = new FormData(form);
    
    // Show loading state
    const submitButton = document.querySelector('.modal-footer .btn-primary');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Ukládám...';
    submitButton.disabled = true;
    
    // Send AJAX request
    fetch('../ajax/update_preview_status.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('Stav objednávky byl úspěšně změněn', 'success');
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(document.querySelector('.modal'));
            modal.hide();
            
            // Reload page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            // Show error message
            showNotification(data.message || 'Chyba při změně stavu', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Chyba při komunikaci se serverem', 'error');
    })
    .finally(() => {
        // Restore button state
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    });
}

/**
 * Show loading state
 */
function showLoadingState() {
    const tableContainer = document.querySelector('.orders-table-container');
    if (!tableContainer) return;
    
    // Add loading overlay
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = '<div class="loading-spinner"></div>';
    
    tableContainer.style.position = 'relative';
    tableContainer.appendChild(overlay);
}

/**
 * Show notification
 */
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * Utility function to get URL parameter
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * Utility function to update URL parameter
 */
function updateUrlParameter(key, value) {
    const url = new URL(window.location);
    if (value) {
        url.searchParams.set(key, value);
    } else {
        url.searchParams.delete(key);
    }
    return url.toString();
}

/**
 * Format date for display
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

/**
 * Format number with thousands separator
 */
function formatNumber(number) {
    return new Intl.NumberFormat('cs-CZ').format(number);
}

/**
 * Initialize order expansion functionality
 */
function initializeOrderExpansion() {
    // Make toggleOrderDetail function globally available
    window.toggleOrderDetail = toggleOrderDetail;
}

/**
 * Toggle order detail expansion
 */
function toggleOrderDetail(orderId) {
    const detailRow = document.getElementById(`detail-${orderId}`);
    const expandIcon = document.querySelector(`[onclick="toggleOrderDetail(${orderId})"] .expand-icon`);

    if (!detailRow) {
        console.error('Detail row not found for order:', orderId);
        return;
    }

    if (expandedOrders.has(orderId)) {
        // Collapse
        detailRow.style.display = 'none';
        expandIcon.classList.remove('expanded');
        expandedOrders.delete(orderId);
    } else {
        // Expand
        detailRow.style.display = 'table-row';
        expandIcon.classList.add('expanded');
        expandedOrders.add(orderId);

        // Load order detail if not already loaded
        loadOrderDetail(orderId);
    }
}

/**
 * Load order detail via AJAX
 */
function loadOrderDetail(orderId) {
    const container = document.querySelector(`#detail-${orderId} .order-detail-container`);
    const loadingPlaceholder = container.querySelector('.loading-placeholder');
    const existingContent = container.querySelector('.order-detail-content');

    // If content already loaded, don't reload
    if (existingContent && existingContent.classList.contains('loaded')) {
        return;
    }

    // Show loading state
    if (loadingPlaceholder) {
        loadingPlaceholder.style.display = 'block';
    }

    // Fetch order detail
    fetch(`../ajax/get_order_detail.php?id=${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderOrderDetail(container, data.order, data.items);
            } else {
                showOrderDetailError(container, data.message || 'Chyba při načítání detailu objednávky');
            }
        })
        .catch(error => {
            console.error('Error loading order detail:', error);
            showOrderDetailError(container, 'Chyba při komunikaci se serverem');
        })
        .finally(() => {
            // Hide loading placeholder
            if (loadingPlaceholder) {
                loadingPlaceholder.style.display = 'none';
            }
        });
}

/**
 * Render order detail content
 */
function renderOrderDetail(container, order, items) {
    const content = document.createElement('div');
    content.className = 'order-detail-content loaded';

    content.innerHTML = `
        <div class="row">
            <div class="col-md-8">
                <div class="detail-section">
                    <h6><i class="fas fa-info-circle me-2"></i>Informace o objednávce</h6>
                    <div class="detail-info-grid">
                        <div class="detail-info-item">
                            <span class="detail-info-label">Kód objednávky</span>
                            <span class="detail-info-value">${escapeHtml(order.order_code)}</span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Obchodník</span>
                            <span class="detail-info-value">
                                <span class="sales-rep-prefix">${escapeHtml(order.sales_rep)}</span>
                                ${escapeHtml(order.sales_rep_name || 'Neznámý')}
                            </span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Datum objednávky</span>
                            <span class="detail-info-value">${formatDate(order.order_date)}</span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Stav objednávky</span>
                            <span class="detail-info-value">
                                <span class="status-badge status-${order.status.replace('_', '-')}">${getStatusLabel(order.status)}</span>
                            </span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Stav náhledu</span>
                            <span class="detail-info-value">
                                <span class="status-badge status-${order.preview_status.replace('_', '-')}">${getPreviewStatusLabel(order.preview_status)}</span>
                            </span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Počet položek</span>
                            <span class="detail-info-value"><span class="items-count">${items.length}</span></span>
                        </div>
                    </div>
                    ${order.notes ? `
                    <div class="mt-3">
                        <span class="detail-info-label">Poznámky</span>
                        <p class="mt-2">${escapeHtml(order.notes).replace(/\\n/g, '<br>')}</p>
                    </div>
                    ` : ''}
                </div>

                <div class="detail-section">
                    <h6><i class="fas fa-list me-2"></i>Položky objednávky (${items.length})</h6>
                    ${renderOrderItems(items)}
                </div>
            </div>

            <div class="col-md-4">
                <div class="detail-section">
                    <h6><i class="fas fa-bolt me-2"></i>Rychlé akce</h6>
                    <div class="quick-actions">
                        <button type="button" class="quick-action-btn quick-action-warning"
                                onclick="changeOrderStatus(${order.id}, '${order.preview_status}')">
                            <i class="fas fa-edit"></i>Změnit stav
                        </button>
                        <button type="button" class="quick-action-btn quick-action-primary"
                                onclick="toggleOrderDetail(${order.id})">
                            <i class="fas fa-times"></i>Zavřít detail
                        </button>
                    </div>
                </div>

                <div class="detail-section">
                    <h6><i class="fas fa-clock me-2"></i>Časové údaje</h6>
                    <div class="detail-info-grid">
                        <div class="detail-info-item">
                            <span class="detail-info-label">Vytvořeno</span>
                            <span class="detail-info-value">${formatDateTime(order.created_at)}</span>
                        </div>
                        <div class="detail-info-item">
                            <span class="detail-info-label">Aktualizováno</span>
                            <span class="detail-info-value">${formatDateTime(order.updated_at)}</span>
                        </div>
                        ${order.preview_approved_date ? `
                        <div class="detail-info-item">
                            <span class="detail-info-label">Náhled schválen</span>
                            <span class="detail-info-value">${formatDate(order.preview_approved_date)}</span>
                        </div>
                        ` : ''}
                        ${order.expected_delivery_date ? `
                        <div class="detail-info-item">
                            <span class="detail-info-label">Očekávané dodání</span>
                            <span class="detail-info-value">${formatDate(order.expected_delivery_date)}</span>
                        </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        </div>
    `;

    // Replace loading content with actual content
    const loadingPlaceholder = container.querySelector('.loading-placeholder');
    const existingContent = container.querySelector('.order-detail-content');

    if (existingContent) {
        container.removeChild(existingContent);
    }

    container.appendChild(content);
}

/**
 * Render order items table
 */
function renderOrderItems(items) {
    if (!items || items.length === 0) {
        return '<p class="text-muted text-center">Žádné položky objednávky</p>';
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped items-table">
                <thead>
                    <tr>
                        <th>Katalog</th>
                        <th>Množství</th>
                        <th>Stav zásob</th>
                        <th>Datum objednání</th>
                        <th>Datum naskladnění</th>
                        <th>Technologie</th>
                        <th>Relevantní</th>
                    </tr>
                </thead>
                <tbody>
    `;

    items.forEach(item => {
        const isRelevant = item.is_relevant == 1;
        const rowClass = isRelevant ? '' : 'irrelevant';

        html += `
            <tr class="${rowClass}">
                <td>${escapeHtml(item.catalog_code || '-')}</td>
                <td>${parseFloat(item.quantity).toFixed(4)}</td>
                <td>
                    <span class="badge ${getInventoryStatusClass(item.inventory_status)}">
                        ${getInventoryStatusLabel(item.inventory_status)}
                    </span>
                </td>
                <td>${item.goods_ordered_date ? formatDate(item.goods_ordered_date) : '-'}</td>
                <td>${item.goods_stocked_date ? formatDate(item.goods_stocked_date) : '-'}</td>
                <td>${escapeHtml(item.technology_assignment || '-')}</td>
                <td>
                    ${isRelevant ?
                        '<i class="fas fa-check text-success"></i>' :
                        '<i class="fas fa-times text-danger"></i>'
                    }
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

/**
 * Show order detail error
 */
function showOrderDetailError(container, message) {
    const content = document.createElement('div');
    content.className = 'order-detail-content loaded';
    content.innerHTML = `
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${escapeHtml(message)}
        </div>
    `;

    const existingContent = container.querySelector('.order-detail-content');
    if (existingContent) {
        container.removeChild(existingContent);
    }

    container.appendChild(content);
}

/**
 * Helper functions for status labels
 */
function getStatusLabel(status) {
    const labels = {
        'pending': 'Čeká',
        'in_progress': 'Ve zpracování',
        'completed': 'Dokončeno',
        'cancelled': 'Zrušeno'
    };
    return labels[status] || status;
}

function getPreviewStatusLabel(status) {
    const labels = {
        'not_created': 'Nevytvořen',
        'sent_to_client': 'Odeslán klientovi',
        'approved': 'Schválen'
    };
    return labels[status] || status;
}

function getInventoryStatusLabel(status) {
    const labels = {
        'not_in_stock': 'Není skladem',
        'ordered': 'Objednáno',
        'in_stock': 'Skladem'
    };
    return labels[status] || status;
}

function getInventoryStatusClass(status) {
    const classes = {
        'not_in_stock': 'bg-danger',
        'ordered': 'bg-warning',
        'in_stock': 'bg-success'
    };
    return classes[status] || 'bg-secondary';
}

/**
 * Escape HTML to prevent XSS
 */
function escapeHtml(text) {
    if (!text) return '';
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Format datetime for display
 */
function formatDateTime(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}
