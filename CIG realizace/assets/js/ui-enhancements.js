/**
 * CIG Realizace - UI Enhancements
 * Enhanced user interface components and interactions
 */

class UIEnhancements {
    /**
     * Show loading overlay on element
     */
    static showLoading(element, message = 'Načítání...') {
        const $element = $(element);
        const loader = `
            <div class="loading-overlay" role="status" aria-live="polite">
                <div class="loading-spinner" aria-hidden="true"></div>
                <div class="loading-message">${message}</div>
                <span class="sr-only">Načítání obsahu...</span>
            </div>
        `;
        $element.addClass('position-relative loading').append(loader);
    }
    
    /**
     * Hide loading overlay
     */
    static hideLoading(element) {
        $(element).removeClass('loading').find('.loading-overlay').remove();
    }
    
    /**
     * Show notification with accessibility support
     */
    static showNotification(message, type = 'success', duration = 5000) {
        const notificationId = 'notification-' + Date.now();
        const notification = `
            <div id="${notificationId}" class="notification notification-${type}" role="alert" aria-live="assertive">
                <div class="notification-content">
                    <i class="notification-icon fas fa-${this.getIcon(type)}" aria-hidden="true"></i>
                    <span class="notification-message">${message}</span>
                    <button class="notification-close btn-close" aria-label="Zavřít notifikaci" type="button">&times;</button>
                </div>
            </div>
        `;
        
        $('body').append(notification);
        
        // Auto-hide notification
        setTimeout(() => {
            $(`#${notificationId}`).fadeOut(300, function() {
                $(this).remove();
            });
        }, duration);
        
        // Manual close
        $(`#${notificationId} .notification-close`).on('click', function() {
            $(this).closest('.notification').fadeOut(300, function() {
                $(this).remove();
            });
        });
    }
    
    /**
     * Get icon for notification type
     */
    static getIcon(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
    
    /**
     * Initialize mobile filter panel
     */
    static initMobileFilters() {
        // Create mobile filter toggle if it doesn't exist
        if ($('.mobile-filter-toggle').length === 0 && $('.filter-panel').length > 0) {
            $('body').append(`
                <button class="mobile-filter-toggle d-md-none" aria-label="Otevřít filtry" type="button">
                    <i class="fas fa-filter" aria-hidden="true"></i>
                </button>
                <div class="filter-overlay"></div>
            `);
        }
        
        // Mobile filter toggle
        $(document).on('click', '.mobile-filter-toggle', function() {
            $('.filter-panel').addClass('active');
            $('.filter-overlay').addClass('active');
            $('body').addClass('overflow-hidden');
            
            // Focus first input in filter panel
            setTimeout(() => {
                $('.filter-panel input, .filter-panel select').first().focus();
            }, 300);
        });
        
        // Close filter panel
        $(document).on('click', '.filter-overlay, .filter-close', function() {
            $('.filter-panel').removeClass('active');
            $('.filter-overlay').removeClass('active');
            $('body').removeClass('overflow-hidden');
            $('.mobile-filter-toggle').focus();
        });
        
        // Close on escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('.filter-panel').hasClass('active')) {
                $('.filter-panel').removeClass('active');
                $('.filter-overlay').removeClass('active');
                $('body').removeClass('overflow-hidden');
                $('.mobile-filter-toggle').focus();
            }
        });
    }
    
    /**
     * Initialize keyboard navigation
     */
    static initKeyboardNavigation() {
        // Table row navigation
        $('.order-table tbody tr').attr('tabindex', '0');
        
        $(document).on('keydown', '.order-table tbody tr', function(e) {
            const $current = $(this);
            let $target;
            
            switch(e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    $target = $current.next('tr');
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    $target = $current.prev('tr');
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    $current.find('.order-code-link').click();
                    break;
            }
            
            if ($target && $target.length) {
                $target.focus();
            }
        });
        
        // Calendar navigation
        $('.calendar-day').attr('tabindex', '0');
        
        $(document).on('keydown', '.calendar-day', function(e) {
            const $current = $(this);
            const currentIndex = $('.calendar-day').index($current);
            const daysPerWeek = 7;
            let targetIndex;
            
            switch(e.key) {
                case 'ArrowRight':
                    e.preventDefault();
                    targetIndex = currentIndex + 1;
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    targetIndex = currentIndex - 1;
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    targetIndex = currentIndex + daysPerWeek;
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    targetIndex = currentIndex - daysPerWeek;
                    break;
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if ($current.find('.order-block').length > 0) {
                        $current.find('.order-block').first().click();
                    }
                    break;
            }
            
            if (targetIndex !== undefined) {
                const $target = $('.calendar-day').eq(targetIndex);
                if ($target.length) {
                    $target.focus();
                }
            }
        });
    }
    
    /**
     * Initialize responsive tables
     */
    static initResponsiveTables() {
        $('.table-responsive').each(function() {
            const $table = $(this);
            const $wrapper = $table.parent();
            
            // Add scroll indicators
            if (!$wrapper.hasClass('scroll-indicator-wrapper')) {
                $wrapper.addClass('scroll-indicator-wrapper');
                $wrapper.append('<div class="scroll-indicator scroll-indicator-left"></div>');
                $wrapper.append('<div class="scroll-indicator scroll-indicator-right"></div>');
            }
            
            // Update scroll indicators
            function updateScrollIndicators() {
                const scrollLeft = $table.scrollLeft();
                const scrollWidth = $table[0].scrollWidth;
                const clientWidth = $table[0].clientWidth;
                
                $('.scroll-indicator-left').toggle(scrollLeft > 0);
                $('.scroll-indicator-right').toggle(scrollLeft < scrollWidth - clientWidth);
            }
            
            $table.on('scroll', updateScrollIndicators);
            updateScrollIndicators();
        });
    }
    
    /**
     * Initialize tooltips with better accessibility
     */
    static initTooltips() {
        $('[data-bs-toggle="tooltip"]').each(function() {
            const $element = $(this);
            const tooltipId = 'tooltip-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            $element.attr('aria-describedby', tooltipId);
            
            new bootstrap.Tooltip(this, {
                template: `<div class="tooltip" id="${tooltipId}" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>`
            });
        });
    }
    
    /**
     * Initialize all UI enhancements
     */
    static init() {
        this.initMobileFilters();
        this.initKeyboardNavigation();
        this.initResponsiveTables();
        this.initTooltips();
        
        // Add skip link if it doesn't exist
        if ($('.skip-link').length === 0) {
            $('body').prepend('<a href="#main-content" class="skip-link">Přejít na hlavní obsah</a>');
            $('#main-content, main, .main-content').first().attr('id', 'main-content');
        }
        
        // Announce page changes to screen readers
        this.announcePageChange();
    }
    
    /**
     * Announce page changes to screen readers
     */
    static announcePageChange() {
        const pageTitle = document.title;
        const announcement = `Stránka načtena: ${pageTitle}`;
        
        // Create or update live region
        let $liveRegion = $('#page-announcements');
        if ($liveRegion.length === 0) {
            $liveRegion = $('<div id="page-announcements" aria-live="polite" aria-atomic="true" class="sr-only"></div>');
            $('body').append($liveRegion);
        }
        
        $liveRegion.text(announcement);
    }
}

/**
 * Enhanced form validation with accessibility
 */
class FormValidator {
    /**
     * Validate order form
     */
    static validateOrderForm(formData) {
        const errors = [];
        
        if (!formData.order_code || formData.order_code.length < 3) {
            errors.push({
                field: 'order_code',
                message: 'Kód objednávky musí mít alespoň 3 znaky'
            });
        }
        
        if (!formData.sales_rep) {
            errors.push({
                field: 'sales_rep',
                message: 'Obchodní zástupce je povinný'
            });
        }
        
        if (!formData.order_date) {
            errors.push({
                field: 'order_date',
                message: 'Datum objednávky je povinné'
            });
        }
        
        return errors;
    }
    
    /**
     * Display validation errors with accessibility
     */
    static displayErrors(errors, container) {
        const $container = $(container);
        
        // Clear previous errors
        $('.form-control').removeClass('is-invalid');
        $('.error-message').remove();
        
        if (errors.length === 0) {
            $container.hide();
            return;
        }
        
        // Create error summary
        const errorSummary = `
            <div class="alert alert-danger" role="alert" aria-labelledby="error-summary-title">
                <h4 id="error-summary-title" class="alert-heading">Opravte následující chyby:</h4>
                <ul class="mb-0">
                    ${errors.map(error => `<li><a href="#${error.field}">${error.message}</a></li>`).join('')}
                </ul>
            </div>
        `;
        
        $container.html(errorSummary).show();
        
        // Mark invalid fields and add error messages
        errors.forEach(error => {
            const $field = $(`#${error.field}, [name="${error.field}"]`);
            $field.addClass('is-invalid');
            $field.attr('aria-invalid', 'true');
            
            const errorId = `${error.field}-error`;
            $field.attr('aria-describedby', errorId);
            
            const errorMessage = `<div id="${errorId}" class="error-message invalid-feedback">${error.message}</div>`;
            $field.after(errorMessage);
        });
        
        // Focus first error field
        if (errors.length > 0) {
            const firstErrorField = $(`#${errors[0].field}, [name="${errors[0].field}"]`);
            firstErrorField.focus();
        }
        
        // Scroll to error summary
        $container[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    
    /**
     * Clear validation errors
     */
    static clearErrors() {
        $('.form-control').removeClass('is-invalid').removeAttr('aria-invalid aria-describedby');
        $('.error-message').remove();
        $('.alert-danger').remove();
    }
}

// Initialize when DOM is ready
$(document).ready(function() {
    UIEnhancements.init();
    
    // Re-initialize on AJAX content load
    $(document).on('contentLoaded', function() {
        UIEnhancements.initTooltips();
        UIEnhancements.initResponsiveTables();
    });
});
