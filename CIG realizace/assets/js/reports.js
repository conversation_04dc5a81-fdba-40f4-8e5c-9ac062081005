/**
 * Reports and Statistics JavaScript
 */

const Reports = {
    charts: {},
    
    init: function() {
        console.log('Reports module initialized');
        this.setupEventListeners();
        this.setupAutoRefresh();
    },
    
    setupEventListeners: function() {
        // Export buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[onclick*="exportReport"]')) {
                e.preventDefault();
                const format = e.target.getAttribute('onclick').match(/exportReport\('(\w+)'\)/)[1];
                this.exportReport(format);
            }
        });
        
        // Filter changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.report-filter')) {
                this.applyFilters();
            }
        });
    },
    
    setupAutoRefresh: function() {
        // Auto-refresh activity feed every 60 seconds
        if (document.getElementById('activityFeed')) {
            setInterval(() => {
                this.refreshActivityFeed();
            }, 60000);
        }
    },
    
    initStatusChart: function(data) {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;
        
        const labels = [];
        const values = [];
        const colors = [];
        
        const statusColors = {
            'not_created': '#dc3545',
            'sent_to_client': '#ffc107',
            'approved': '#28a745'
        };
        
        const statusLabels = {
            'not_created': 'Nevytvořeno',
            'sent_to_client': 'Odesláno klientovi',
            'approved': 'Schváleno'
        };
        
        for (const [status, count] of Object.entries(data)) {
            labels.push(statusLabels[status] || status);
            values.push(count);
            colors.push(statusColors[status] || '#6c757d');
        }
        
        this.charts.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: colors,
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    },
    
    initSalesRepChart: function(data) {
        const ctx = document.getElementById('salesRepChart');
        if (!ctx) return;
        
        const labels = data.map(item => item.sales_rep_name || 'Neznámý');
        const values = data.map(item => parseInt(item.count));
        
        // Generate colors
        const colors = this.generateColors(labels.length);
        
        this.charts.salesRepChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Počet objednávek',
                    data: values,
                    backgroundColor: colors.background,
                    borderColor: colors.border,
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.y} objednávek`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        },
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    },
    
    initActivityChart: function(data) {
        const ctx = document.getElementById('activityChart');
        if (!ctx) return;
        
        // Process data for time series
        const processedData = this.processActivityData(data);
        
        this.charts.activityChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: processedData.labels,
                datasets: [{
                    label: 'Počet akcí',
                    data: processedData.values,
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#667eea',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    },
    
    generateColors: function(count) {
        const baseColors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c',
            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
            '#fa709a', '#fee140', '#a8edea', '#fed6e3'
        ];
        
        const background = [];
        const border = [];
        
        for (let i = 0; i < count; i++) {
            const color = baseColors[i % baseColors.length];
            background.push(color + '80'); // Add transparency
            border.push(color);
        }
        
        return { background, border };
    },
    
    processActivityData: function(data) {
        // Group data by date
        const grouped = {};
        
        data.forEach(item => {
            const date = new Date(item.created_at).toLocaleDateString('cs-CZ');
            grouped[date] = (grouped[date] || 0) + 1;
        });
        
        // Sort by date and prepare for chart
        const sorted = Object.entries(grouped).sort((a, b) => {
            return new Date(a[0].split('.').reverse().join('-')) - 
                   new Date(b[0].split('.').reverse().join('-'));
        });
        
        return {
            labels: sorted.map(item => item[0]),
            values: sorted.map(item => item[1])
        };
    },
    
    exportReport: function(format) {
        this.showNotification('Připravuje se export...', 'info');
        
        const params = new URLSearchParams({
            format: format,
            page: window.location.pathname.split('/').pop().replace('.php', '')
        });
        
        // Add current filters if any
        const filters = this.getCurrentFilters();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) params.append(key, value);
        });
        
        const exportUrl = `../ajax/export_report.php?${params.toString()}`;
        
        // Create temporary link and trigger download
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `report_${format}_${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        this.showNotification(`Report byl exportován do ${format.toUpperCase()}`, 'success');
    },
    
    getCurrentFilters: function() {
        const filters = {};
        
        document.querySelectorAll('.report-filter').forEach(filter => {
            if (filter.value) {
                filters[filter.name] = filter.value;
            }
        });
        
        return filters;
    },
    
    applyFilters: function() {
        const filters = this.getCurrentFilters();
        
        // Show loading
        this.showLoading();
        
        // Make AJAX request to update data
        fetch('../ajax/get_filtered_data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(filters)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updateCharts(data.data);
                this.updateTables(data.data);
            } else {
                this.showNotification('Chyba při načítání dat', 'error');
            }
        })
        .catch(error => {
            console.error('Error applying filters:', error);
            this.showNotification('Chyba při aplikaci filtrů', 'error');
        })
        .finally(() => {
            this.hideLoading();
        });
    },
    
    updateCharts: function(data) {
        // Update existing charts with new data
        if (this.charts.statusChart && data.status_data) {
            this.charts.statusChart.data.datasets[0].data = Object.values(data.status_data);
            this.charts.statusChart.update();
        }
        
        if (this.charts.salesRepChart && data.sales_rep_data) {
            this.charts.salesRepChart.data.datasets[0].data = data.sales_rep_data.map(item => item.count);
            this.charts.salesRepChart.update();
        }
    },
    
    updateTables: function(data) {
        // Update table content if present
        const tableBody = document.querySelector('#dataTable tbody');
        if (tableBody && data.table_data) {
            tableBody.innerHTML = data.table_data;
        }
    },
    
    refreshActivityFeed: function() {
        const feedContainer = document.getElementById('activityFeed');
        if (!feedContainer) return;
        
        fetch('../ajax/get_activity_feed.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                feedContainer.innerHTML = data.html;
                this.animateNewItems();
            }
        })
        .catch(error => {
            console.error('Error refreshing activity feed:', error);
        });
    },
    
    animateNewItems: function() {
        const newItems = document.querySelectorAll('.activity-item:not(.animated)');
        newItems.forEach((item, index) => {
            item.classList.add('animated');
            setTimeout(() => {
                item.classList.add('fade-in');
            }, index * 100);
        });
    },
    
    showLoading: function() {
        const loadingHtml = `
            <div class="loading-spinner">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Načítání...</span>
                </div>
            </div>
        `;
        
        document.querySelectorAll('.chart-container').forEach(container => {
            container.innerHTML = loadingHtml;
        });
    },
    
    hideLoading: function() {
        // Loading will be hidden when charts are redrawn
    },
    
    showNotification: function(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (typeof Chart !== 'undefined') {
        // Set Chart.js defaults
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.color = '#495057';
        Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(0,0,0,0.8)';
        Chart.defaults.plugins.tooltip.titleColor = '#fff';
        Chart.defaults.plugins.tooltip.bodyColor = '#fff';
        Chart.defaults.plugins.tooltip.cornerRadius = 8;
    }
});

// Export for global use
window.Reports = Reports;
