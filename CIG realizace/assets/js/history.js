/**
 * History Management JavaScript
 * CIG Realizace - Phase 06
 */

const HistoryManager = {
    orderId: null,
    
    init: function(orderId) {
        this.orderId = orderId;
        this.bindEvents();
        this.loadHistory();
    },
    
    bindEvents: function() {
        // Filter change events
        $('#actionTypeFilter, #limitFilter').on('change', () => {
            this.loadHistory();
        });
        
        // Refresh button
        $('#refreshHistory').on('click', () => {
            this.loadHistory();
        });
        
        // Export button
        $('#exportHistory').on('click', () => {
            this.exportHistory();
        });
    },
    
    loadHistory: function() {
        const actionType = $('#actionTypeFilter').val();
        const limit = $('#limitFilter').val();
        
        // Show loading
        $('#historyLoading').show();
        $('#historyContainer').hide();
        $('#historyError').hide();
        
        $.ajax({
            url: '../ajax/get_order_history.php',
            method: 'GET',
            data: {
                order_id: this.orderId,
                action_type: actionType,
                limit: limit === '0' ? '' : limit
            },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.renderHistory(response.history);
                } else {
                    this.showError(response.message);
                }
            },
            error: (xhr, status, error) => {
                console.error('Error loading history:', error);
                this.showError('Chyba při načítání historie objednávky');
            },
            complete: () => {
                $('#historyLoading').hide();
            }
        });
    },
    
    renderHistory: function(history) {
        const container = $('#historyContainer');
        container.empty();
        
        if (history.length === 0) {
            container.html(`
                <div class="history-empty">
                    <i class="fas fa-history"></i>
                    <h5>Žádná historie</h5>
                    <p>Pro tuto objednávku zatím neexistují žádné záznamy historie.</p>
                </div>
            `);
        } else {
            history.forEach(entry => {
                const historyItem = this.createHistoryItem(entry);
                container.append(historyItem);
            });
        }
        
        container.show();
    },
    
    createHistoryItem: function(entry) {
        const actionClass = entry.action_type.replace(/_/g, '-');
        
        let additionalDataHtml = '';
        if (entry.additional_data) {
            additionalDataHtml = this.renderAdditionalData(entry.additional_data);
        }
        
        let valueChangeHtml = '';
        if (entry.old_value !== null && entry.new_value !== null && 
            entry.old_value !== entry.new_value) {
            valueChangeHtml = `
                <div class="value-change mt-2">
                    <span class="old-value">${this.escapeHtml(entry.old_value)}</span>
                    <i class="fas fa-arrow-right change-arrow"></i>
                    <span class="new-value">${this.escapeHtml(entry.new_value)}</span>
                </div>
            `;
        }
        
        return $(`
            <div class="history-item action-${actionClass} fade-in">
                <div class="history-header">
                    <div class="history-action">
                        <i class="${entry.icon} text-${entry.color}"></i>
                        ${this.getActionLabel(entry.action_type)}
                    </div>
                    <div class="history-meta">
                        <i class="fas fa-clock"></i>
                        ${entry.formatted_date} (${entry.time_ago})
                    </div>
                </div>
                <div class="history-description">
                    ${this.escapeHtml(entry.description)}
                    ${valueChangeHtml}
                </div>
                <div class="history-user">
                    <i class="fas fa-user"></i>
                    ${this.escapeHtml(entry.user.full_name || entry.user.username)} 
                    <span class="badge bg-secondary ms-2">${this.getRoleLabel(entry.user.role)}</span>
                </div>
                ${additionalDataHtml}
            </div>
        `);
    },
    
    renderAdditionalData: function(data) {
        let html = '<div class="additional-data">';
        
        for (const [key, value] of Object.entries(data)) {
            if (value !== null && value !== '') {
                const label = this.getDataLabel(key);
                html += `
                    <div class="data-row">
                        <span class="data-label">${label}:</span>
                        <span class="data-value">${this.escapeHtml(value)}</span>
                    </div>
                `;
            }
        }
        
        html += '</div>';
        return html;
    },
    
    getActionLabel: function(actionType) {
        const labels = {
            'preview_status_change': 'Změna stavu náhledu',
            'delivery_date_change': 'Změna termínu dodání',
            'technology_assignment': 'Přiřazení technologie',
            'item_relevance_change': 'Změna relevance položky',
            'inventory_status_change': 'Změna stavu zásob',
            'order_completion': 'Dokončení objednávky',
            'order_created': 'Vytvoření objednávky',
            'order_updated': 'Aktualizace objednávky'
        };
        
        return labels[actionType] || actionType;
    },
    
    getRoleLabel: function(role) {
        const labels = {
            'admin': 'Administrátor',
            'obchodnik': 'Obchodník',
            'grafik': 'Grafik',
            'realizator': 'Realizátor'
        };
        
        return labels[role] || role;
    },
    
    getDataLabel: function(key) {
        const labels = {
            'notes': 'Poznámka',
            'old_status_label': 'Původní stav',
            'new_status_label': 'Nový stav',
            'old_date_formatted': 'Původní datum',
            'new_date_formatted': 'Nové datum',
            'item_id': 'ID položky',
            'catalog_code': 'Kód katalogu',
            'completed_at': 'Dokončeno'
        };
        
        return labels[key] || key;
    },
    
    showError: function(message) {
        $('#historyErrorMessage').text(message);
        $('#historyError').show();
        $('#historyContainer').hide();
    },
    
    exportHistory: function() {
        const actionType = $('#actionTypeFilter').val();
        const limit = $('#limitFilter').val();
        
        // Create export URL
        const params = new URLSearchParams({
            order_id: this.orderId,
            action_type: actionType,
            limit: limit === '0' ? '' : limit,
            export: 'csv'
        });
        
        const exportUrl = `../ajax/get_order_history.php?${params.toString()}`;
        
        // Create temporary link and trigger download
        const link = document.createElement('a');
        link.href = exportUrl;
        link.download = `historie_objednavky_${this.orderId}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // Show success message
        this.showNotification('Historie byla exportována', 'success');
    },
    
    showNotification: function(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'}-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(notification);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.alert('close');
        }, 5000);
    },
    
    escapeHtml: function(text) {
        if (text === null || text === undefined) return '';
        
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
};

// Auto-refresh history every 30 seconds if page is visible
let historyRefreshInterval;

document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        if (historyRefreshInterval) {
            clearInterval(historyRefreshInterval);
        }
    } else {
        if (HistoryManager.orderId) {
            historyRefreshInterval = setInterval(() => {
                HistoryManager.loadHistory();
            }, 30000);
        }
    }
});

// Start auto-refresh when page loads
$(document).ready(function() {
    if (!document.hidden && HistoryManager.orderId) {
        historyRefreshInterval = setInterval(() => {
            HistoryManager.loadHistory();
        }, 30000);
    }
});
