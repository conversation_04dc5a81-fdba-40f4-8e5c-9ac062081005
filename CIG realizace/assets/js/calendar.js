/**
 * Calendar JavaScript
 * CIG Realizace - Phase 05
 */

let currentCalendarData = {};
let currentTooltip = null;

// Load calendar data via AJAX
function loadCalendarData(params) {
    const container = $('#calendar-container');
    
    // Show loading state
    container.html(`
        <div class="calendar-loading">
            <div class="text-center">
                <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                <p>Nač<PERSON>t<PERSON>m kalendář...</p>
            </div>
        </div>
    `);
    
    $.ajax({
        url: '../ajax/get_calendar_data.php',
        method: 'GET',
        data: params,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                currentCalendarData = response.data;
                renderCalendar(params, response.data);
            } else {
                showCalendarError(response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Calendar AJAX Error:', error);
            showCalendarError('Chyba při načítání kalendáře');
        }
    });
}

// Render calendar based on view type
function renderCalendar(params, data) {
    const container = $('#calendar-container');
    
    switch (params.view) {
        case 'month':
            renderMonthView(container, params, data);
            break;
        case 'week':
            renderWeekView(container, params, data);
            break;
        case 'day':
            renderDayView(container, params, data);
            break;
        default:
            renderMonthView(container, params, data);
    }
    
    // Initialize event handlers
    initCalendarEvents();
}

// Render month view
function renderMonthView(container, params, data) {
    const daysInMonth = new Date(params.year, params.month, 0).getDate();
    const firstDayWeekday = new Date(params.year, params.month - 1, 1).getDay();
    const adjustedFirstDay = firstDayWeekday === 0 ? 7 : firstDayWeekday; // Convert Sunday from 0 to 7
    
    let html = `
        <div class="calendar-grid">
            <div class="calendar-header-row">
                <div class="day-header">Po</div>
                <div class="day-header">Út</div>
                <div class="day-header">St</div>
                <div class="day-header">Čt</div>
                <div class="day-header">Pá</div>
                <div class="day-header">So</div>
                <div class="day-header">Ne</div>
            </div>
            <div class="calendar-body">
    `;
    
    // Calculate total cells needed
    const totalCells = Math.ceil((daysInMonth + adjustedFirstDay - 1) / 7) * 7;
    
    for (let i = 1; i <= totalCells; i++) {
        const dayNumber = i - adjustedFirstDay + 1;
        const isCurrentMonth = dayNumber >= 1 && dayNumber <= daysInMonth;
        const date = isCurrentMonth ? 
            `${params.year}-${String(params.month).padStart(2, '0')}-${String(dayNumber).padStart(2, '0')}` : '';
        
        const isToday = isCurrentMonth && 
            dayNumber === new Date().getDate() && 
            params.month === new Date().getMonth() + 1 && 
            params.year === new Date().getFullYear();
        
        const dayClasses = [
            'calendar-day',
            isCurrentMonth ? '' : 'other-month',
            isToday ? 'today' : ''
        ].filter(Boolean).join(' ');
        
        html += `
            <div class="${dayClasses}" data-date="${date}">
                <div class="day-number">${isCurrentMonth ? dayNumber : ''}</div>
                <div class="day-orders">
                    ${isCurrentMonth ? renderDayOrders(date, data.orders) : ''}
                </div>
            </div>
        `;
    }
    
    html += `
            </div>
        </div>
    `;
    
    // Add legend
    html += renderLegend();
    
    container.html(html);
}

// Render orders for a specific day
function renderDayOrders(date, orders) {
    const dayOrders = orders.filter(order => {
        const startDate = new Date(order.start_date);
        const endDate = new Date(order.end_date);
        const currentDate = new Date(date);
        
        return currentDate >= startDate && currentDate <= endDate;
    });
    
    if (dayOrders.length === 0) {
        return '';
    }
    
    let html = '';
    const maxVisible = 3;
    
    for (let i = 0; i < Math.min(dayOrders.length, maxVisible); i++) {
        const order = dayOrders[i];
        const isStart = date === order.start_date;
        const isEnd = date === order.end_date;
        const isMultiDay = order.start_date !== order.end_date;
        
        const blockClasses = [
            'order-block',
            `sales-rep-${order.sales_rep}`,
            isMultiDay ? 'multi-day' : '',
            isStart ? 'start-day' : '',
            isEnd ? 'end-day' : '',
            getOrderStatusClass(order)
        ].filter(Boolean).join(' ');
        
        html += `
            <div class="${blockClasses}" 
                 data-order-id="${order.id}"
                 data-start-date="${order.start_date}"
                 data-end-date="${order.end_date}"
                 title="${order.order_code}">
                <div class="order-code">${order.order_code}</div>
                ${isStart ? `<div class="order-sales-rep">${order.sales_rep_name}</div>` : ''}
            </div>
        `;
    }
    
    // Show "more" indicator if there are additional orders
    if (dayOrders.length > maxVisible) {
        const remaining = dayOrders.length - maxVisible;
        html += `
            <div class="more-orders" data-date="${date}">
                +${remaining} dalších
            </div>
        `;
    }
    
    return html;
}

// Get order status class for styling
function getOrderStatusClass(order) {
    const today = new Date();
    const endDate = new Date(order.end_date);
    const daysUntilDeadline = Math.ceil((endDate - today) / (1000 * 60 * 60 * 24));
    
    if (daysUntilDeadline < 0) {
        return 'overdue';
    } else if (daysUntilDeadline <= 3) {
        return 'due-soon';
    } else {
        return 'on-track';
    }
}

// Render legend
function renderLegend() {
    return `
        <div class="calendar-legend mt-3">
            <div class="legend-item">
                <div class="legend-color" style="background-color: #3498db;"></div>
                <span>Vláďa (VP)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e74c3c;"></div>
                <span>Jirka (J)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #2ecc71;"></div>
                <span>Nikol (NK)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #f39c12;"></div>
                <span>Mirka (MR)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #9b59b6;"></div>
                <span>Daniela (D)</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #1abc9c;"></div>
                <span>Czech Image (CI)</span>
            </div>
        </div>
    `;
}

// Render week view (simplified for now)
function renderWeekView(container, params, data) {
    container.html(`
        <div class="calendar-week-view">
            <div class="text-center p-5">
                <i class="fas fa-calendar-week fa-3x text-muted mb-3"></i>
                <h4>Týdenní pohled</h4>
                <p class="text-muted">Týdenní pohled bude implementován v další verzi</p>
            </div>
        </div>
    `);
}

// Render day view (simplified for now)
function renderDayView(container, params, data) {
    container.html(`
        <div class="calendar-day-view">
            <div class="text-center p-5">
                <i class="fas fa-calendar-day fa-3x text-muted mb-3"></i>
                <h4>Denní pohled</h4>
                <p class="text-muted">Denní pohled bude implementován v další verzi</p>
            </div>
        </div>
    `);
}

// Show calendar error
function showCalendarError(message) {
    $('#calendar-container').html(`
        <div class="calendar-empty">
            <i class="fas fa-exclamation-triangle text-warning"></i>
            <h4>Chyba při načítání kalendáře</h4>
            <p>${message}</p>
            <button class="btn btn-primary" onclick="location.reload()">
                <i class="fas fa-refresh me-1"></i>Zkusit znovu
            </button>
        </div>
    `);
}

// Initialize calendar event handlers
function initCalendarEvents() {
    // Order block click
    $('.order-block').on('click', function(e) {
        e.stopPropagation();
        const orderId = $(this).data('order-id');
        if (orderId) {
            window.open(`../orders/detail.php?id=${orderId}`, '_blank');
        }
    });
    
    // Order block hover for tooltip
    $('.order-block').on('mouseenter', function(e) {
        const orderId = $(this).data('order-id');
        if (orderId) {
            showOrderTooltip(e, orderId);
        }
    });
    
    $('.order-block').on('mouseleave', function() {
        hideOrderTooltip();
    });
    
    // More orders click
    $('.more-orders').on('click', function() {
        const date = $(this).data('date');
        showDayOrdersModal(date);
    });
}

// Show order tooltip
function showOrderTooltip(event, orderId) {
    const order = currentCalendarData.orders.find(o => o.id == orderId);
    if (!order) return;
    
    hideOrderTooltip();
    
    const tooltip = $(`
        <div class="order-tooltip">
            <div class="tooltip-header">${order.order_code}</div>
            <div class="tooltip-content">
                <strong>Obchodník:</strong> ${order.sales_rep_name}<br>
                <strong>Položky:</strong> ${order.items_count}<br>
                <strong>Technologie:</strong> ${order.technologies || 'Nepřiřazeno'}<br>
                <strong>Termín:</strong> ${formatDate(order.end_date)}
            </div>
        </div>
    `);
    
    $('body').append(tooltip);
    
    // Position tooltip
    const rect = event.target.getBoundingClientRect();
    tooltip.css({
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX + (rect.width / 2) - (tooltip.outerWidth() / 2)
    });
    
    currentTooltip = tooltip;
}

// Hide order tooltip
function hideOrderTooltip() {
    if (currentTooltip) {
        currentTooltip.remove();
        currentTooltip = null;
    }
}

// Show day orders modal
function showDayOrdersModal(date) {
    const dayOrders = currentCalendarData.orders.filter(order => {
        const startDate = new Date(order.start_date);
        const endDate = new Date(order.end_date);
        const currentDate = new Date(date);
        
        return currentDate >= startDate && currentDate <= endDate;
    });
    
    let modalContent = `
        <div class="modal fade" id="dayOrdersModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Objednávky pro ${formatDate(date)}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
    `;
    
    if (dayOrders.length === 0) {
        modalContent += '<p class="text-muted">Žádné objednávky pro tento den</p>';
    } else {
        modalContent += '<div class="list-group">';
        dayOrders.forEach(order => {
            modalContent += `
                <a href="../orders/detail.php?id=${order.id}" target="_blank" 
                   class="list-group-item list-group-item-action">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">${order.order_code}</h6>
                        <small class="sales-rep-${order.sales_rep}">${order.sales_rep_name}</small>
                    </div>
                    <p class="mb-1">Položky: ${order.items_count}</p>
                    <small>Termín: ${formatDate(order.end_date)}</small>
                </a>
            `;
        });
        modalContent += '</div>';
    }
    
    modalContent += `
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zavřít</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal and add new one
    $('#dayOrdersModal').remove();
    $('body').append(modalContent);
    $('#dayOrdersModal').modal('show');
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ');
}

// Update delivery date (for future drag & drop functionality)
function updateDeliveryDate(orderId, newDate) {
    $.ajax({
        url: '../ajax/update_delivery_date.php',
        method: 'POST',
        data: {
            order_id: orderId,
            delivery_date: newDate
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                showNotification('success', 'Termín dodání byl aktualizován');
                // Reload calendar data
                location.reload();
            } else {
                showNotification('error', response.message);
            }
        },
        error: function(xhr, status, error) {
            showNotification('error', 'Chyba při aktualizaci termínu');
            console.error('AJAX Error:', error);
        }
    });
}

// Show notification
function showNotification(type, message) {
    const notification = $(`
        <div class="notification ${type} fade-in">
            <div class="d-flex align-items-center p-3">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="$(this).closest('.notification').remove()"></button>
            </div>
        </div>
    `);
    
    $('body').append(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.fadeOut(() => notification.remove());
    }, 5000);
}
