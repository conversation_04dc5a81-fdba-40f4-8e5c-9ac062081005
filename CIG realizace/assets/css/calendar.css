/**
 * Calendar Styles
 * CIG Realizace - Phase 05
 */

/* Calendar Header */
.calendar-navigation {
    display: flex;
    align-items: center;
    gap: 15px;
}

.calendar-navigation h3 {
    min-width: 200px;
    text-align: center;
    margin: 0;
    color: #495057;
}

/* Calendar Grid */
.calendar-grid {
    width: 100%;
    border-collapse: collapse;
}

.calendar-header-row {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.day-header {
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.calendar-body {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e9ecef;
}

.calendar-day {
    background: white;
    min-height: 120px;
    padding: 8px;
    position: relative;
    border: 1px solid #e9ecef;
    transition: background-color 0.3s ease;
}

.calendar-day:hover {
    background-color: #f8f9fa;
}

.calendar-day.other-month {
    background-color: #f8f9fa;
    color: #6c757d;
}

.calendar-day.today {
    background-color: #e3f2fd;
    border: 2px solid #2196f3;
}

.day-number {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: #495057;
}

.calendar-day.other-month .day-number {
    color: #adb5bd;
}

.calendar-day.today .day-number {
    color: #1976d2;
    font-weight: 700;
}

/* Order Blocks */
.day-orders {
    display: flex;
    flex-direction: column;
    gap: 2px;
    max-height: 90px;
    overflow: hidden;
}

.order-block {
    background: #667eea;
    color: white;
    padding: 4px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.order-block:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.order-block.multi-day {
    border-radius: 0;
}

.order-block.start-day {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.order-block.end-day {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

/* Sales Representative Colors */
.order-block.sales-rep-VP { background-color: #3498db; }
.order-block.sales-rep-J { background-color: #e74c3c; }
.order-block.sales-rep-NK { background-color: #2ecc71; }
.order-block.sales-rep-MR { background-color: #f39c12; }
.order-block.sales-rep-D { background-color: #9b59b6; }
.order-block.sales-rep-CI { background-color: #1abc9c; }

/* Status Indicators */
.order-block.overdue {
    border: 2px solid #e74c3c;
    animation: pulse 2s infinite;
}

.order-block.due-soon {
    border: 2px solid #f39c12;
}

.order-block.on-track {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Order Block Content */
.order-code {
    font-weight: 600;
    margin-bottom: 1px;
}

.order-sales-rep {
    font-size: 0.7rem;
    opacity: 0.9;
}

.order-delivery {
    font-size: 0.7rem;
    opacity: 0.8;
}

/* More Orders Indicator */
.more-orders {
    background: #6c757d;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    text-align: center;
    cursor: pointer;
    margin-top: 2px;
}

.more-orders:hover {
    background: #5a6268;
}

/* Week View */
.calendar-week-view .calendar-day {
    min-height: 150px;
}

.calendar-week-view .order-block {
    font-size: 0.8rem;
    padding: 6px 8px;
}

/* Day View */
.calendar-day-view {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.day-view-order {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.day-view-order.sales-rep-VP { border-left-color: #3498db; }
.day-view-order.sales-rep-J { border-left-color: #e74c3c; }
.day-view-order.sales-rep-NK { border-left-color: #2ecc71; }
.day-view-order.sales-rep-MR { border-left-color: #f39c12; }
.day-view-order.sales-rep-D { border-left-color: #9b59b6; }
.day-view-order.sales-rep-CI { border-left-color: #1abc9c; }

/* Order Tooltip */
.order-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    z-index: 1000;
    max-width: 250px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.order-tooltip::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid #333;
}

.tooltip-header {
    font-weight: 600;
    margin-bottom: 5px;
    border-bottom: 1px solid #555;
    padding-bottom: 5px;
}

.tooltip-content {
    line-height: 1.4;
}

/* Legend */
.calendar-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

/* Loading States */
.calendar-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    color: #6c757d;
}

.calendar-loading i {
    font-size: 2rem;
    margin-bottom: 10px;
}

/* Empty State */
.calendar-empty {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

.calendar-empty i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .calendar-navigation {
        flex-direction: column;
        gap: 10px;
    }
    
    .calendar-navigation h3 {
        min-width: auto;
    }
    
    .calendar-day {
        min-height: 80px;
        padding: 4px;
    }
    
    .day-orders {
        max-height: 60px;
    }
    
    .order-block {
        font-size: 0.7rem;
        padding: 2px 4px;
    }
    
    .day-header {
        padding: 10px 5px;
        font-size: 0.8rem;
    }
    
    .calendar-legend {
        justify-content: center;
    }
    
    .legend-item {
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .calendar-body {
        font-size: 0.8rem;
    }
    
    .calendar-day {
        min-height: 60px;
        padding: 2px;
    }
    
    .day-number {
        font-size: 0.8rem;
    }
    
    .order-block {
        font-size: 0.65rem;
        padding: 1px 3px;
    }
    
    .day-orders {
        max-height: 45px;
    }
}

/* Print Styles */
@media print {
    .calendar-navigation,
    .btn,
    .card-header {
        display: none !important;
    }
    
    .calendar-day {
        min-height: 100px;
        break-inside: avoid;
    }
    
    .order-block {
        background: #f8f9fa !important;
        color: #333 !important;
        border: 1px solid #dee2e6 !important;
    }
}
