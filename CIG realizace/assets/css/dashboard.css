/* CIG Realizace - Dashboard Styles */

/* General Layout */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border-radius: 0.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* Sidebar */
.list-group-item {
    border: none;
    border-radius: 0;
}

.list-group-item.active {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.list-group-item:hover:not(.active) {
    background-color: #f8f9fa;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    font-size: 0.875rem;
}

/* Badges */
.badge {
    font-size: 0.75rem;
}

/* Buttons */
.btn {
    border-radius: 0.375rem;
}

.btn-sm {
    font-size: 0.8125rem;
}

/* Stats Cards */
.stats-card {
    transition: transform 0.2s;
}

.stats-card:hover {
    transform: translateY(-2px);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stats-card .text-muted {
    font-size: 0.875rem;
}

/* Import specific styles */
.import-container {
    max-width: 800px;
    margin: 0 auto;
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.3s;
}

.file-upload-area:hover {
    border-color: #0d6efd;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
}

/* Progress bars */
.progress {
    height: 0.5rem;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 0.5rem;
}

/* Modal improvements */
.modal-content {
    border: none;
    border-radius: 0.5rem;
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Status indicators */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-pending {
    background-color: #ffc107;
}

.status-in-progress {
    background-color: #0d6efd;
}

.status-completed {
    background-color: #198754;
}

.status-cancelled {
    background-color: #dc3545;
}

/* Order status specific */
.order-status {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

/* Priority indicators */
.priority-high {
    color: #dc3545;
}

.priority-medium {
    color: #ffc107;
}

.priority-low {
    color: #198754;
}

/* Custom scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #dee2e6;
    border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #adb5bd;
}
