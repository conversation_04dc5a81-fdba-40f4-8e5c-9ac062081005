<?php
/**
 * Database Configuration
 * CIG Realizace - Order Management System
 */

// Database configuration
$db_config = [
    'type' => 'sqlite', // 'mysql' or 'sqlite'
    'host' => 'localhost',
    'dbname' => 'cig_realizace',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4',
    'sqlite_path' => __DIR__ . '/../data/cig_realizace.db',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];

// Environment-specific configurations
if (isset($_SERVER['HTTP_HOST'])) {
    $host = $_SERVER['HTTP_HOST'];

    // Production environment (Webglobe hosting)
    if (strpos($host, 'webglobe') !== false || strpos($host, 'your-domain.cz') !== false) {
        $db_config['type'] = 'mysql';
        $db_config['host'] = 'localhost';
        $db_config['username'] = 'your_db_user';
        $db_config['password'] = 'your_db_password';
        $db_config['dbname'] = 'your_db_name';
    }

    // Development environment - use SQLite for testing
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        $db_config['type'] = 'sqlite';
        $db_config['sqlite_path'] = __DIR__ . '/../data/cig_realizace.db';
    }
}

/**
 * Get database connection
 * @return PDO
 * @throws PDOException
 */
function getDbConnection() {
    global $db_config;

    try {
        if ($db_config['type'] === 'sqlite') {
            // Create data directory if it doesn't exist
            $dataDir = dirname($db_config['sqlite_path']);
            if (!is_dir($dataDir)) {
                mkdir($dataDir, 0755, true);
            }

            $dsn = "sqlite:" . $db_config['sqlite_path'];
            $pdo = new PDO($dsn, null, null, $db_config['options']);
        } else {
            // MySQL connection
            $dsn = "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}";
            $pdo = new PDO($dsn, $db_config['username'], $db_config['password'], $db_config['options']);
        }
        return $pdo;
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new PDOException("Chyba připojení k databázi. Kontaktujte administrátora.");
    }
}

/**
 * Test database connection
 * @return bool
 */
function testDbConnection() {
    try {
        $pdo = getDbConnection();
        $pdo->query("SELECT 1");
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Execute SQL file
 * @param string $filename
 * @return bool
 */
function executeSqlFile($filename) {
    try {
        $pdo = getDbConnection();
        $sql = file_get_contents($filename);
        
        // Split by semicolon and execute each statement
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        return true;
    } catch (Exception $e) {
        error_log("SQL execution failed: " . $e->getMessage());
        return false;
    }
}
