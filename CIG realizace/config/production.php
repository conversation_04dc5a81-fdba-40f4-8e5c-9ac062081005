<?php
/**
 * CIG Realizace - Production Configuration
 * Production environment settings and security hardening
 */

// Production error handling
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/../logs/php_errors.log');
error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED);

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

// Content Security Policy
$csp = "default-src 'self'; " .
       "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
       "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
       "font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; " .
       "img-src 'self' data: https:; " .
       "connect-src 'self'; " .
       "frame-ancestors 'none';";
header("Content-Security-Policy: $csp");

// Production configuration
$production_config = [
    'environment' => 'production',
    
    // Database configuration
    'database' => [
        'host' => getenv('DB_HOST') ?: 'localhost',
        'dbname' => getenv('DB_NAME') ?: 'cig_realizace_prod',
        'username' => getenv('DB_USERNAME') ?: '',
        'password' => getenv('DB_PASSWORD') ?: '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
        ]
    ],
    
    // Email configuration for Webglobe hosting
    'email' => [
        'smtp_host' => 'smtp.webglobe.com',
        'smtp_port' => 587,
        'smtp_security' => 'tls',
        'smtp_username' => getenv('SMTP_USERNAME') ?: '',
        'smtp_password' => getenv('SMTP_PASSWORD') ?: '',
        'from_email' => getenv('FROM_EMAIL') ?: '<EMAIL>',
        'from_name' => 'CIG Realizace System',
        'reply_to' => getenv('REPLY_TO_EMAIL') ?: '<EMAIL>'
    ],
    
    // Security settings
    'security' => [
        'session_timeout' => 3600, // 1 hour
        'max_login_attempts' => 5,
        'lockout_duration' => 900, // 15 minutes
        'password_min_length' => 8,
        'require_strong_passwords' => true,
        'csrf_token_lifetime' => 3600,
        'secure_cookies' => true,
        'httponly_cookies' => true,
        'samesite_cookies' => 'Strict'
    ],
    
    // File upload settings
    'upload' => [
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['csv', 'xlsx', 'xls'],
        'upload_path' => __DIR__ . '/../uploads/',
        'scan_uploads' => true
    ],
    
    // Logging settings
    'logging' => [
        'level' => 'WARNING',
        'max_file_size' => 50 * 1024 * 1024, // 50MB
        'max_files' => 10,
        'log_path' => __DIR__ . '/../logs/'
    ],
    
    // Performance settings
    'performance' => [
        'enable_compression' => true,
        'cache_static_files' => true,
        'minify_output' => true,
        'database_query_cache' => true
    ],
    
    // Backup settings
    'backup' => [
        'enabled' => true,
        'frequency' => 'daily',
        'retention_days' => 30,
        'backup_path' => '/var/backups/cig-realizace/',
        'include_uploads' => true
    ]
];

/**
 * CSRF Protection Class
 */
class CSRFProtection {
    public static function generateToken() {
        if (!isset($_SESSION['csrf_token']) || 
            !isset($_SESSION['csrf_token_time']) || 
            (time() - $_SESSION['csrf_token_time']) > $GLOBALS['production_config']['security']['csrf_token_lifetime']) {
            
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
            $_SESSION['csrf_token_time'] = time();
        }
        return $_SESSION['csrf_token'];
    }
    
    public static function validateToken($token) {
        if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // Check token lifetime
        if ((time() - $_SESSION['csrf_token_time']) > $GLOBALS['production_config']['security']['csrf_token_lifetime']) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public static function getTokenField() {
        $token = self::generateToken();
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }
}

/**
 * Input Sanitization Class
 */
class InputSanitizer {
    public static function sanitizeString($input, $maxLength = null) {
        $sanitized = htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
        if ($maxLength && strlen($sanitized) > $maxLength) {
            $sanitized = substr($sanitized, 0, $maxLength);
        }
        return $sanitized;
    }
    
    public static function sanitizeEmail($email) {
        $email = filter_var(trim($email), FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) ? $email : false;
    }
    
    public static function sanitizeInt($input, $min = null, $max = null) {
        $int = filter_var($input, FILTER_SANITIZE_NUMBER_INT);
        $int = filter_var($int, FILTER_VALIDATE_INT);
        
        if ($int === false) {
            return false;
        }
        
        if ($min !== null && $int < $min) {
            return false;
        }
        
        if ($max !== null && $int > $max) {
            return false;
        }
        
        return $int;
    }
    
    public static function sanitizeFloat($input, $min = null, $max = null) {
        $float = filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
        $float = filter_var($float, FILTER_VALIDATE_FLOAT);
        
        if ($float === false) {
            return false;
        }
        
        if ($min !== null && $float < $min) {
            return false;
        }
        
        if ($max !== null && $float > $max) {
            return false;
        }
        
        return $float;
    }
    
    public static function sanitizeFilename($filename) {
        // Remove path traversal attempts
        $filename = basename($filename);
        
        // Remove dangerous characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
        
        // Limit length
        if (strlen($filename) > 255) {
            $filename = substr($filename, 0, 255);
        }
        
        return $filename;
    }
}

/**
 * Security Logger Class
 */
class SecurityLogger {
    private static $logFile;
    
    public static function init() {
        self::$logFile = $GLOBALS['production_config']['logging']['log_path'] . 'security.log';
        
        // Create log directory if it doesn't exist
        $logDir = dirname(self::$logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    public static function logSecurityEvent($event, $details = [], $severity = 'INFO') {
        if (!self::$logFile) {
            self::init();
        }
        
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        $userId = $_SESSION['user_id'] ?? 'anonymous';
        
        $logEntry = [
            'timestamp' => $timestamp,
            'severity' => $severity,
            'event' => $event,
            'user_id' => $userId,
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'details' => $details
        ];
        
        $logLine = json_encode($logEntry) . "\n";
        file_put_contents(self::$logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // Also log to system log for critical events
        if ($severity === 'CRITICAL' || $severity === 'ERROR') {
            error_log("CIG Security Event: $event - " . json_encode($details));
        }
    }
    
    public static function logLoginAttempt($username, $success, $reason = '') {
        self::logSecurityEvent('login_attempt', [
            'username' => $username,
            'success' => $success,
            'reason' => $reason
        ], $success ? 'INFO' : 'WARNING');
    }
    
    public static function logSuspiciousActivity($activity, $details = []) {
        self::logSecurityEvent('suspicious_activity', array_merge([
            'activity' => $activity
        ], $details), 'WARNING');
    }
    
    public static function logDataAccess($table, $action, $recordId = null) {
        self::logSecurityEvent('data_access', [
            'table' => $table,
            'action' => $action,
            'record_id' => $recordId
        ], 'INFO');
    }
}

/**
 * Rate Limiting Class
 */
class RateLimiter {
    private static $limits = [
        'login' => ['requests' => 5, 'window' => 900], // 5 attempts per 15 minutes
        'api' => ['requests' => 100, 'window' => 3600], // 100 requests per hour
        'search' => ['requests' => 50, 'window' => 300] // 50 searches per 5 minutes
    ];
    
    public static function checkLimit($action, $identifier = null) {
        if (!isset(self::$limits[$action])) {
            return true;
        }
        
        $identifier = $identifier ?: ($_SERVER['REMOTE_ADDR'] ?? 'unknown');
        $limit = self::$limits[$action];
        
        $cacheKey = "rate_limit_{$action}_{$identifier}";
        $attempts = self::getAttempts($cacheKey);
        
        if ($attempts >= $limit['requests']) {
            SecurityLogger::logSuspiciousActivity('rate_limit_exceeded', [
                'action' => $action,
                'identifier' => $identifier,
                'attempts' => $attempts,
                'limit' => $limit['requests']
            ]);
            return false;
        }
        
        self::incrementAttempts($cacheKey, $limit['window']);
        return true;
    }
    
    private static function getAttempts($key) {
        // Simple file-based cache for rate limiting
        $cacheFile = sys_get_temp_dir() . '/' . md5($key) . '.cache';
        
        if (!file_exists($cacheFile)) {
            return 0;
        }
        
        $data = json_decode(file_get_contents($cacheFile), true);
        
        if (!$data || $data['expires'] < time()) {
            unlink($cacheFile);
            return 0;
        }
        
        return $data['attempts'];
    }
    
    private static function incrementAttempts($key, $window) {
        $cacheFile = sys_get_temp_dir() . '/' . md5($key) . '.cache';
        $attempts = self::getAttempts($key) + 1;
        
        $data = [
            'attempts' => $attempts,
            'expires' => time() + $window
        ];
        
        file_put_contents($cacheFile, json_encode($data), LOCK_EX);
    }
}

// Initialize security components
SecurityLogger::init();

// Set global configuration
$GLOBALS['production_config'] = $production_config;

// Configure session security
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_secure', $production_config['security']['secure_cookies'] ? '1' : '0');
    ini_set('session.cookie_httponly', $production_config['security']['httponly_cookies'] ? '1' : '0');
    ini_set('session.cookie_samesite', $production_config['security']['samesite_cookies']);
    ini_set('session.use_strict_mode', '1');
    ini_set('session.cookie_lifetime', '0'); // Session cookies only
    
    session_start();
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration']) || 
        (time() - $_SESSION['last_regeneration']) > 300) { // Every 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Enable output compression if configured
if ($production_config['performance']['enable_compression'] && !ob_get_level()) {
    ob_start('ob_gzhandler');
}

return $production_config;
?>
