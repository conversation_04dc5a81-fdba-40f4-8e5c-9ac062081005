<?php
/**
 * Email Configuration for CIG Realizace
 * Phase 08 - Email Notifications System
 */

// Email configuration array
$email_config = [
    // SMTP Settings for Webglobe hosting
    'smtp_host' => 'smtp.webglobe.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>', // Update with actual email
    'smtp_password' => '', // Update with actual password
    'smtp_encryption' => 'tls', // tls or ssl
    'smtp_timeout' => 30,
    
    // From settings
    'from_email' => '<EMAIL>',
    'from_name' => 'CIG Realizace System',
    
    // Reply-to settings
    'reply_to_email' => '<EMAIL>',
    'reply_to_name' => 'CIG Image Support',
    
    // Email settings
    'charset' => 'UTF-8',
    'content_type' => 'text/html',
    
    // Queue settings
    'queue_batch_size' => 50, // Number of emails to process in one batch
    'queue_retry_delay' => 300, // Seconds to wait before retry (5 minutes)
    'queue_max_attempts' => 3,
    
    // Template settings
    'template_cache_enabled' => true,
    'template_cache_ttl' => 3600, // Cache templates for 1 hour
    
    // Tracking settings
    'tracking_enabled' => true,
    'tracking_pixel_enabled' => true,
    'click_tracking_enabled' => true,
    
    // Rate limiting
    'rate_limit_enabled' => true,
    'rate_limit_per_hour' => 100, // Max emails per hour per recipient
    'rate_limit_per_day' => 500, // Max emails per day per recipient
    
    // Development settings
    'debug_mode' => false, // Set to true for development
    'log_all_emails' => true,
    'test_mode' => false, // If true, emails won't be actually sent
    'test_recipient' => '<EMAIL>', // All emails go here in test mode
];

// Environment-specific overrides
if (isLocalEnvironment()) {
    // Development environment settings
    $email_config['debug_mode'] = true;
    $email_config['test_mode'] = true;
    $email_config['test_recipient'] = 'developer@localhost';
    $email_config['smtp_host'] = 'localhost';
    $email_config['smtp_port'] = 1025; // MailHog or similar
    $email_config['smtp_username'] = '';
    $email_config['smtp_password'] = '';
    $email_config['smtp_encryption'] = '';
}

/**
 * Check if we're in local development environment
 */
function isLocalEnvironment() {
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return strpos($host, 'localhost') !== false || 
           strpos($host, '127.0.0.1') !== false ||
           strpos($host, '.local') !== false;
}

/**
 * Get email configuration
 */
function getEmailConfig() {
    global $email_config;
    return $email_config;
}

/**
 * Get SMTP configuration for PHPMailer
 */
function getSMTPConfig() {
    global $email_config;
    
    return [
        'host' => $email_config['smtp_host'],
        'port' => $email_config['smtp_port'],
        'username' => $email_config['smtp_username'],
        'password' => $email_config['smtp_password'],
        'encryption' => $email_config['smtp_encryption'],
        'timeout' => $email_config['smtp_timeout'],
        'from_email' => $email_config['from_email'],
        'from_name' => $email_config['from_name'],
        'reply_to_email' => $email_config['reply_to_email'],
        'reply_to_name' => $email_config['reply_to_name'],
        'charset' => $email_config['charset']
    ];
}

/**
 * Get queue configuration
 */
function getQueueConfig() {
    global $email_config;
    
    return [
        'batch_size' => $email_config['queue_batch_size'],
        'retry_delay' => $email_config['queue_retry_delay'],
        'max_attempts' => $email_config['queue_max_attempts']
    ];
}

/**
 * Check if email functionality is enabled
 */
function isEmailEnabled() {
    global $email_config;
    
    // Check if required SMTP settings are configured
    return !empty($email_config['smtp_host']) && 
           !empty($email_config['from_email']);
}

/**
 * Get email template directory
 */
function getEmailTemplateDir() {
    return __DIR__ . '/../templates/email/';
}

/**
 * Get email tracking URL
 */
function getEmailTrackingUrl($trackingId) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return $protocol . '://' . $host . '/track_email.php?id=' . $trackingId;
}

/**
 * Get unsubscribe URL
 */
function getUnsubscribeUrl($userId, $token) {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return $protocol . '://' . $host . '/unsubscribe.php?user=' . $userId . '&token=' . $token;
}

/**
 * Generate unsubscribe token for user
 */
function generateUnsubscribeToken($userId) {
    return hash('sha256', $userId . '_' . date('Y-m-d') . '_unsubscribe_salt');
}

/**
 * Validate unsubscribe token
 */
function validateUnsubscribeToken($userId, $token) {
    $expectedToken = generateUnsubscribeToken($userId);
    return hash_equals($expectedToken, $token);
}
?>
