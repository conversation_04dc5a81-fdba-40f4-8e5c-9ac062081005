#!/usr/bin/env php
<?php
/**
 * Cron Job: Send Email Notifications
 * CIG Realizace - Phase 08
 * 
 * This script processes the email queue and sends pending notifications.
 * Should be run every 5-10 minutes via cron.
 * 
 * Cron example:
 * */5 * * * * /usr/bin/php /path/to/cig-realizace/cron/send_notifications.php
 */

// Set script timeout
set_time_limit(300); // 5 minutes

// Change to script directory
chdir(dirname(__FILE__));

// Include required files
require_once '../config/database.php';
require_once '../config/email.php';
require_once '../includes/email_functions.php';

// Log start
$logFile = '../logs/email_cron.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

logMessage("Email notification cron started");

try {
    // Check if email system is enabled
    if (!isEmailEnabled()) {
        logMessage("Email system is not enabled - skipping");
        exit(0);
    }
    
    // Process email queue
    $batchSize = getEmailConfig()['queue_batch_size'] ?? 50;
    $processed = processEmailQueue($batchSize);
    
    logMessage("Processed $processed emails from queue");
    
    // Clean old emails (older than 30 days)
    $cleaned = cleanOldEmailQueue(30);
    if ($cleaned > 0) {
        logMessage("Cleaned $cleaned old emails from queue");
    }
    
    // Get queue statistics for monitoring
    $stats = getEmailQueueStats();
    $pending = $stats['pending'] ?? 0;
    $failed = $stats['failed'] ?? 0;
    
    if ($pending > 100) {
        logMessage("WARNING: High number of pending emails: $pending");
    }
    
    if ($failed > 50) {
        logMessage("WARNING: High number of failed emails: $failed");
    }
    
    logMessage("Email notification cron completed successfully");
    
} catch (Exception $e) {
    logMessage("ERROR: " . $e->getMessage());
    logMessage("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

exit(0);
?>
