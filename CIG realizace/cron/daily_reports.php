#!/usr/bin/env php
<?php
/**
 * Cron Job: Daily Reports and Alerts
 * CIG Realizace - Phase 08
 * 
 * This script sends daily summary reports and overdue order alerts.
 * Should be run once daily, preferably in the morning.
 * 
 * Cron example:
 * 0 8 * * * /usr/bin/php /path/to/cig-realizace/cron/daily_reports.php
 */

// Set script timeout
set_time_limit(600); // 10 minutes

// Change to script directory
chdir(dirname(__FILE__));

// Include required files
require_once '../config/database.php';
require_once '../config/email.php';
require_once '../includes/email_functions.php';

// Log file
$logFile = '../logs/daily_reports.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

function logMessage($message) {
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

logMessage("Daily reports cron started");

try {
    // Check if email system is enabled
    if (!isEmailEnabled()) {
        logMessage("Email system is not enabled - skipping");
        exit(0);
    }
    
    $totalSent = 0;
    
    // 1. Send daily summary reports
    logMessage("Sending daily summary reports...");
    $dailySummariesSent = sendDailySummaryEmails();
    $totalSent += $dailySummariesSent;
    logMessage("Sent $dailySummariesSent daily summary reports");
    
    // 2. Check for overdue orders and send alerts
    logMessage("Checking for overdue orders...");
    $overdueOrders = getOverdueOrders();
    
    if (!empty($overdueOrders)) {
        logMessage("Found " . count($overdueOrders) . " overdue orders");
        
        $alertsSent = sendOverdueOrdersAlert();
        if ($alertsSent) {
            $totalSent += $alertsSent;
            logMessage("Sent overdue order alerts to administrators");
        } else {
            logMessage("No overdue alerts sent (no recipients or sending failed)");
        }
    } else {
        logMessage("No overdue orders found");
    }
    
    // 3. Weekly summary (only on Mondays)
    if (date('N') == 1) { // Monday
        logMessage("Sending weekly summary reports...");
        $weeklySummariesSent = sendWeeklySummaryEmails();
        $totalSent += $weeklySummariesSent;
        logMessage("Sent $weeklySummariesSent weekly summary reports");
    }
    
    // 4. System health check
    logMessage("Performing system health check...");
    $queueStats = getEmailQueueStats();
    
    // Check for stuck emails
    $stuckEmails = getStuckEmails();
    if (!empty($stuckEmails)) {
        logMessage("WARNING: Found " . count($stuckEmails) . " stuck emails in queue");
        
        // Reset stuck emails
        $reset = resetStuckEmails();
        logMessage("Reset $reset stuck emails");
    }
    
    // Check queue health
    $pending = $queueStats['pending'] ?? 0;
    $failed = $queueStats['failed'] ?? 0;
    
    if ($pending > 200) {
        logMessage("WARNING: High number of pending emails: $pending");
        sendSystemAlert("High email queue", "There are $pending pending emails in the queue.");
    }
    
    if ($failed > 100) {
        logMessage("WARNING: High number of failed emails: $failed");
        sendSystemAlert("High email failures", "There are $failed failed emails in the queue.");
    }
    
    // 5. Database maintenance
    logMessage("Performing database maintenance...");
    
    // Clean old delivery tracking records (older than 90 days)
    $cleanedTracking = cleanOldDeliveryTracking(90);
    if ($cleanedTracking > 0) {
        logMessage("Cleaned $cleanedTracking old delivery tracking records");
    }
    
    // Clean old email statistics (older than 365 days)
    $cleanedStats = cleanOldEmailStatistics(365);
    if ($cleanedStats > 0) {
        logMessage("Cleaned $cleanedStats old email statistics records");
    }
    
    logMessage("Daily reports cron completed successfully. Total emails sent: $totalSent");
    
} catch (Exception $e) {
    logMessage("ERROR: " . $e->getMessage());
    logMessage("Stack trace: " . $e->getTraceAsString());
    
    // Send error alert to administrators
    try {
        sendSystemAlert("Daily reports cron error", $e->getMessage());
    } catch (Exception $alertError) {
        logMessage("Failed to send error alert: " . $alertError->getMessage());
    }
    
    exit(1);
}

exit(0);

/**
 * Send weekly summary emails
 */
function sendWeeklySummaryEmails() {
    try {
        $pdo = getDbConnection();
        
        // Get users who should receive weekly summary
        $stmt = $pdo->prepare("
            SELECT u.id, u.email, u.full_name
            FROM users u
            JOIN user_notification_preferences unp ON u.id = unp.user_id
            WHERE u.is_active = 1 AND unp.weekly_summary = 1
            AND (unp.last_weekly_summary IS NULL OR 
                 DATE(unp.last_weekly_summary) < DATE('now', '-6 days'))
        ");
        
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            return 0;
        }
        
        $emailManager = new EmailManager();
        $sent = 0;
        
        foreach ($users as $user) {
            // Get weekly statistics
            $stats = getWeeklyStats($user['id']);
            
            // Skip if no activity
            if ($stats['total_activity'] == 0) {
                continue;
            }
            
            // Prepare template data
            $templateData = [
                'user_name' => $user['full_name'],
                'week_start' => date('d.m.Y', strtotime('monday this week')),
                'week_end' => date('d.m.Y', strtotime('sunday this week')),
                'new_orders' => $stats['new_orders'],
                'completed_orders' => $stats['completed_orders'],
                'pending_previews' => $stats['pending_previews'],
                'overdue_orders' => $stats['overdue_orders'],
                'total_activity' => $stats['total_activity'],
                'dashboard_url' => getDashboardUrl()
            ];
            
            if ($emailManager->queueEmail(
                $user['email'],
                $user['full_name'],
                'Týdenní souhrn - ' . date('d.m.Y'),
                'weekly_summary',
                $templateData,
                5
            )) {
                $sent++;
                
                // Update last weekly summary timestamp
                updateLastSummaryTimestamp($user['id'], 'weekly');
            }
        }
        
        return $sent;
        
    } catch (Exception $e) {
        logMessage("Error sending weekly summaries: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get weekly statistics for user
 */
function getWeeklyStats($userId) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(CASE WHEN oh.action = 'order_created' AND DATE(oh.created_at) >= DATE('now', 'weekday 0', '-6 days') THEN 1 END) as new_orders,
                COUNT(CASE WHEN oh.action = 'order_completed' AND DATE(oh.created_at) >= DATE('now', 'weekday 0', '-6 days') THEN 1 END) as completed_orders,
                COUNT(CASE WHEN oh.action = 'preview_status_change' AND DATE(oh.created_at) >= DATE('now', 'weekday 0', '-6 days') THEN 1 END) as pending_previews,
                COUNT(CASE WHEN DATE(oh.created_at) >= DATE('now', 'weekday 0', '-6 days') THEN 1 END) as total_activity
            FROM order_history oh
            WHERE oh.user_id = ?
        ");
        
        $stmt->execute([$userId]);
        $stats = $stmt->fetch();
        
        // Get overdue orders count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as overdue_orders
            FROM orders o
            WHERE o.sales_rep = (SELECT username FROM users WHERE id = ?)
            AND o.expected_delivery_date < DATE('now')
            AND o.is_completed = 0
        ");
        
        $stmt->execute([$userId]);
        $overdue = $stmt->fetch();
        
        $stats['overdue_orders'] = $overdue['overdue_orders'];
        
        return $stats;
        
    } catch (Exception $e) {
        logMessage("Error getting weekly stats: " . $e->getMessage());
        return [
            'new_orders' => 0,
            'completed_orders' => 0,
            'pending_previews' => 0,
            'overdue_orders' => 0,
            'total_activity' => 0
        ];
    }
}

/**
 * Get stuck emails (processing for more than 1 hour)
 */
function getStuckEmails() {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            SELECT id FROM notification_queue 
            WHERE status = 'processing' 
            AND updated_at < datetime('now', '-1 hour')
        ");
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
        
    } catch (Exception $e) {
        logMessage("Error getting stuck emails: " . $e->getMessage());
        return [];
    }
}

/**
 * Reset stuck emails back to pending
 */
function resetStuckEmails() {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            UPDATE notification_queue 
            SET status = 'pending', updated_at = CURRENT_TIMESTAMP
            WHERE status = 'processing' 
            AND updated_at < datetime('now', '-1 hour')
        ");
        
        $stmt->execute();
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        logMessage("Error resetting stuck emails: " . $e->getMessage());
        return 0;
    }
}

/**
 * Send system alert to administrators
 */
function sendSystemAlert($subject, $message) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("SELECT email, full_name FROM users WHERE role = 'admin' AND is_active = 1");
        $stmt->execute();
        $admins = $stmt->fetchAll();
        
        if (empty($admins)) {
            return false;
        }
        
        $emailManager = new EmailManager();
        
        foreach ($admins as $admin) {
            $emailManager->queueEmail(
                $admin['email'],
                $admin['full_name'],
                "CIG Realizace Alert: $subject",
                'system_alert',
                [
                    'admin_name' => $admin['full_name'],
                    'alert_subject' => $subject,
                    'alert_message' => $message,
                    'alert_time' => date('d.m.Y H:i:s'),
                    'dashboard_url' => getDashboardUrl()
                ],
                1 // High priority
            );
        }
        
        return true;
        
    } catch (Exception $e) {
        logMessage("Error sending system alert: " . $e->getMessage());
        return false;
    }
}

/**
 * Clean old delivery tracking records
 */
function cleanOldDeliveryTracking($daysOld) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            DELETE FROM email_delivery_tracking 
            WHERE created_at < datetime('now', '-' || ? || ' days')
        ");
        
        $stmt->execute([$daysOld]);
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        logMessage("Error cleaning old delivery tracking: " . $e->getMessage());
        return 0;
    }
}

/**
 * Clean old email statistics
 */
function cleanOldEmailStatistics($daysOld) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            DELETE FROM email_statistics 
            WHERE date < date('now', '-' || ? || ' days')
        ");
        
        $stmt->execute([$daysOld]);
        return $stmt->rowCount();
        
    } catch (Exception $e) {
        logMessage("Error cleaning old email statistics: " . $e->getMessage());
        return 0;
    }
}

/**
 * Update last summary timestamp
 */
function updateLastSummaryTimestamp($userId, $type) {
    try {
        $pdo = getDbConnection();
        
        $column = $type === 'daily' ? 'last_daily_summary' : 'last_weekly_summary';
        
        $stmt = $pdo->prepare("
            UPDATE user_notification_preferences 
            SET $column = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
            WHERE user_id = ?
        ");
        
        $stmt->execute([$userId]);
        
    } catch (Exception $e) {
        logMessage("Error updating last summary timestamp: " . $e->getMessage());
    }
}

/**
 * Get dashboard URL
 */
function getDashboardUrl() {
    return 'http://localhost:8000/dashboard.php'; // Update for production
}
?>
