# CIG Realizace - Administrátorská příručka

## Obsah
1. [Úvod](#úvod)
2. [Instalace a konfigurace](#instalace-a-konfigurace)
3. [Spr<PERSON><PERSON> uživatelů](#správa-uživatelů)
4. [Spr<PERSON>va systému](#správa-systému)
5. [<PERSON><PERSON> systé<PERSON>](#email-systém)
6. [Z<PERSON>lohování a obnova](#zálohování-a-obnova)
7. [Monitoring a logování](#monitoring-a-logování)
8. [Bezpečnost](#bezpečnost)
9. [Řešení problémů](#řešení-problémů)
10. [Údržba systému](#údržba-systému)

## Úvod

Tato příručka je určena pro administrátory systému CIG Realizace. Obsahuje informace o instalaci, konfiguraci, správě a údrž<PERSON>ě systému.

### Systémové požadavky
- **PHP:** 8.3 nebo vyšší
- **Databáze:** MySQL 8.0+ nebo SQLite 3.35+
- **Webový server:** Apache 2.4+ nebo Nginx 1.18+
- **Operační systém:** Linux (doporučeno Ubuntu 20.04+)

## Instalace a konfigurace

### Rychlá instalace

#### 1. Stažení a rozbalení
```bash
# Stáhněte zdrojové kódy
git clone <repository-url> cig-realizace
cd cig-realizace
```

#### 2. Spuštění instalace
```bash
# Pro vývojové prostředí
php -S localhost:8000

# Pro produkční nasazení
sudo ./deploy/deploy.sh
```

#### 3. Webová instalace
1. Přejděte na `http://your-domain/install.php`
2. Postupujte podle pokynů instalátora
3. Zadejte databázové údaje
4. Vytvořte administrátorský účet

### Manuální konfigurace

#### Databáze
Upravte soubor `config/database.php`:
```php
$config = [
    'host' => 'localhost',
    'dbname' => 'cig_realizace',
    'username' => 'db_user',
    'password' => 'db_password',
    'charset' => 'utf8mb4'
];
```

#### Email systém
Upravte soubor `config/email.php`:
```php
$email_config = [
    'smtp_host' => 'smtp.webglobe.com',
    'smtp_port' => 587,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'your_password'
];
```

### Produkční konfigurace

#### Environment soubor
Vytvořte soubor `.env` v root adresáři:
```env
DB_HOST=localhost
DB_NAME=cig_realizace_prod
DB_USERNAME=cig_user
DB_PASSWORD=secure_password
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=email_password
```

#### Apache Virtual Host
```apache
<VirtualHost *:80>
    ServerName cigimage.cz
    DocumentRoot /var/www/cig-realizace
    
    <Directory /var/www/cig-realizace>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Bezpečnostní hlavičky
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</VirtualHost>
```

## Správa uživatelů

### Přístup ke správě uživatelů
1. Přihlaste se jako administrátor
2. Přejděte na **Administrace → Uživatelé**

### Vytvoření nového uživatele
1. Klikněte na **"Přidat uživatele"**
2. Vyplňte povinné údaje:
   - Uživatelské jméno (unikátní)
   - Celé jméno
   - Email
   - Role
   - Heslo
3. Klikněte **"Vytvořit uživatele"**

### Úprava uživatele
1. V seznamu uživatelů klikněte na **"Upravit"**
2. Změňte potřebné údaje
3. Pro změnu hesla zaškrtněte **"Změnit heslo"**
4. Uložte změny

### Deaktivace uživatele
1. V seznamu uživatelů klikněte na **"Deaktivovat"**
2. Potvrďte akci
3. Uživatel se nebude moci přihlásit

### Uživatelské role

#### Admin
- Plný přístup ke všem funkcím
- Správa uživatelů
- Systémové nastavení
- Všechny reporty

#### Obchodník
- Správa objednávek
- Import CSV
- Obchodní reporty
- Vlastní objednávky

#### Grafik
- Správa náhledů
- Grafické materiály
- Komunikace s klienty

#### Realizátor
- Výrobní proces
- Správa zásob
- Dokončování objednávek

## Správa systému

### Import dat

#### CSV Import
1. Přejděte na **Administrace → Import**
2. Vyberte CSV soubor
3. Zkontrolujte mapování sloupců
4. Spusťte import
5. Zkontrolujte výsledky

#### Formát CSV souboru
```csv
"Číslo dokladu","Katalog","Množství","Datum vystavení","Obchodník"
"25VP-001","CAT-001","5","2025-01-15","VP"
```

### Správa objednávek

#### Hromadné operace
- Export všech objednávek
- Hromadná změna stavů
- Archivace starých objednávek

#### Statistiky
- Přehled výkonnosti
- Analýza trendů
- Reporty podle období

### Systémové nastavení

#### Obecné nastavení
- Název systému
- Výchozí jazyk
- Časová zóna
- Formát data

#### Workflow nastavení
- Výchozí termíny dodání
- Automatické přechody stavů
- Notifikační pravidla

## Email systém

### Konfigurace SMTP

#### Webglobe hosting
```php
'smtp_host' => 'smtp.webglobe.com',
'smtp_port' => 587,
'smtp_security' => 'tls',
'smtp_username' => '<EMAIL>',
'smtp_password' => 'your_password'
```

#### Testování konfigurace
```bash
php test_email_system.php
```

### Správa email fronty

#### Monitoring fronty
1. Přejděte na **Administrace → Email fronta**
2. Zkontrolujte čekající emaily
3. Sledujte statistiky doručení

#### Manuální zpracování
```bash
cd /var/www/cig-realizace
php cron/send_notifications.php
```

### Cron jobs

#### Nastavení automatického zpracování
```bash
# Každých 5 minut - zpracování email fronty
*/5 * * * * www-data cd /var/www/cig-realizace && php cron/send_notifications.php

# Denně v 6:00 - denní reporty
0 6 * * * www-data cd /var/www/cig-realizace && php cron/daily_reports.php
```

#### Instalace cron jobů
```bash
sudo ./setup_cron.sh
```

## Zálohování a obnova

### Automatické zálohování

#### Databáze
```bash
#!/bin/bash
# Denní záloha databáze
mysqldump -u username -p password cig_realizace > backup_$(date +%Y%m%d).sql
```

#### Soubory
```bash
#!/bin/bash
# Záloha souborů
tar -czf backup_files_$(date +%Y%m%d).tar.gz /var/www/cig-realizace
```

### Manuální záloha

#### Kompletní záloha
```bash
# Záloha databáze
mysqldump -u username -p cig_realizace > database_backup.sql

# Záloha souborů
tar -czf files_backup.tar.gz /var/www/cig-realizace
```

### Obnova ze zálohy

#### Obnova databáze
```bash
mysql -u username -p cig_realizace < database_backup.sql
```

#### Obnova souborů
```bash
tar -xzf files_backup.tar.gz -C /var/www/
```

## Monitoring a logování

### Log soubory

#### Umístění logů
- **PHP chyby:** `logs/php_errors.log`
- **Email systém:** `logs/email_cron.log`
- **Bezpečnost:** `logs/security.log`
- **Aplikace:** `logs/application.log`

#### Sledování logů
```bash
# Sledování chyb v reálném čase
tail -f logs/php_errors.log

# Hledání konkrétních chyb
grep "ERROR" logs/application.log
```

### Systémové metriky

#### Výkon databáze
```sql
-- Pomalé dotazy
SHOW PROCESSLIST;

-- Velikost tabulek
SELECT table_name, 
       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'cig_realizace';
```

#### Využití disku
```bash
# Velikost aplikace
du -sh /var/www/cig-realizace

# Velikost logů
du -sh /var/www/cig-realizace/logs
```

### Alerting

#### Email alerty
Systém automaticky odesílá alerty při:
- Chybách v aplikaci
- Problémech s databází
- Překročení limitů

#### Monitoring skript
```bash
#!/bin/bash
# Kontrola dostupnosti aplikace
if ! curl -f http://localhost/ > /dev/null 2>&1; then
    echo "Aplikace není dostupná!" | mail -s "CIG Alert" <EMAIL>
fi
```

## Bezpečnost

### Bezpečnostní opatření

#### Aktualizace systému
```bash
# Aktualizace PHP balíčků
composer update

# Aktualizace systému
sudo apt update && sudo apt upgrade
```

#### Firewall konfigurace
```bash
# Povolení pouze potřebných portů
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### Audit bezpečnosti

#### Kontrola přístupových práv
```bash
# Kontrola oprávnění souborů
find /var/www/cig-realizace -type f -perm /o+w

# Kontrola vlastnictví
ls -la /var/www/cig-realizace
```

#### Analýza logů
```bash
# Podezřelé přihlášení
grep "Failed login" logs/security.log

# Pokusy o SQL injection
grep -i "select\|union\|drop" logs/access.log
```

### SSL certifikát

#### Let's Encrypt
```bash
# Instalace certbot
sudo apt install certbot python3-certbot-apache

# Získání certifikátu
sudo certbot --apache -d cigimage.cz
```

## Řešení problémů

### Časté problémy

#### Aplikace nereaguje
1. Zkontrolujte Apache/Nginx status
2. Ověřte PHP-FPM proces
3. Zkontrolujte logy chyb
4. Restartujte webový server

#### Databázové chyby
1. Zkontrolujte připojení k databázi
2. Ověřte dostupný prostor
3. Zkontrolujte MySQL logy
4. Restartujte MySQL službu

#### Email nefunguje
1. Zkontrolujte SMTP konfiguraci
2. Ověřte síťové připojení
3. Zkontrolujte email frontu
4. Testujte manuální odeslání

### Diagnostické nástroje

#### Kontrola stavu systému
```bash
# Stav služeb
systemctl status apache2
systemctl status mysql

# Využití zdrojů
htop
df -h
```

#### PHP diagnostika
```php
// Zobrazení PHP informací
phpinfo();

// Test databázového připojení
try {
    $pdo = new PDO($dsn, $username, $password);
    echo "Připojení úspěšné";
} catch (PDOException $e) {
    echo "Chyba: " . $e->getMessage();
}
```

## Údržba systému

### Pravidelná údržba

#### Denní úkoly
- Kontrola logů chyb
- Monitoring výkonu
- Ověření záloh

#### Týdenní úkoly
- Čištění starých logů
- Aktualizace bezpečnostních záplat
- Kontrola využití disku

#### Měsíční úkoly
- Kompletní záloha systému
- Audit bezpečnosti
- Optimalizace databáze

### Optimalizace výkonu

#### Databáze
```sql
-- Optimalizace tabulek
OPTIMIZE TABLE orders, order_items, order_history;

-- Analýza indexů
ANALYZE TABLE orders;
```

#### Cache
```bash
# Vymazání cache
rm -rf cache/*

# Restart PHP-FPM
sudo systemctl restart php8.3-fpm
```

### Aktualizace systému

#### Postup aktualizace
1. Vytvořte zálohu
2. Stáhněte novou verzi
3. Spusťte deployment script
4. Otestujte funkcionalita
5. Zkontrolujte logy

#### Rollback
```bash
# Obnova ze zálohy
sudo ./deploy/rollback.sh backup_20250125.tar.gz
```

---

*Tato příručka je aktuální k verzi 1.0 systému CIG Realizace. Pro nejnovější informace kontaktujte vývojový tým.*
