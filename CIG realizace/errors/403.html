<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>403 - P<PERSON><PERSON><PERSON><PERSON> odepřen | CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .error-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            margin: 20px;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 30px;
        }
        
        .error-title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .error-message {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .back-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        
        .error-code {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 30px 0;
            font-family: 'Courier New', monospace;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-ban"></i>
        </div>
        
        <h1 class="error-title">403</h1>
        <h2 class="h4 mb-3">Přístup odepřen</h2>
        
        <p class="error-message">
            Nemáte oprávnění pro přístup k tomuto zdroji. 
            Pokud si myslíte, že se jedná o chybu, kontaktujte administrátora.
        </p>
        
        <div class="error-code">
            <strong>Chyba:</strong> Forbidden Access<br>
            <strong>Čas:</strong> <span id="current-time"></span>
        </div>
        
        <a href="/" class="back-button">
            <i class="fas fa-home me-2"></i>
            Zpět na hlavní stránku
        </a>
        
        <div class="mt-4">
            <small class="text-muted">
                CIG Realizace System | Kontakt: <EMAIL>
            </small>
        </div>
    </div>

    <script>
        // Display current time
        document.getElementById('current-time').textContent = new Date().toLocaleString('cs-CZ');
        
        // Auto-redirect after 10 seconds
        setTimeout(function() {
            window.location.href = '/';
        }, 10000);
    </script>
</body>
</html>
