<?php
/**
 * Advanced Search Page
 * CIG Realizace - Phase 09
 */

// Include authentication check
require_once '../includes/auth_check.php';
require_once '../classes/SearchEngine.php';
require_once '../classes/FilterManager.php';

// Get current user
$current_user = getCurrentUser();
$user_role = $current_user['role'];

// Get search parameters
$query = trim($_GET['q'] ?? '');
$filters = [
    'sales_rep' => $_GET['sales_rep'] ?? '',
    'preview_status' => $_GET['preview_status'] ?? '',
    'status' => $_GET['status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'technology' => $_GET['technology'] ?? '',
    'inventory_status' => $_GET['inventory_status'] ?? '',
    'is_completed' => isset($_GET['is_completed']) ? ($_GET['is_completed'] === '1') : null,
    'overdue_only' => isset($_GET['overdue_only']) ? ($_GET['overdue_only'] === '1') : false
];

// Remove empty filters
$filters = array_filter($filters, function($value) {
    return $value !== '' && $value !== null;
});

$results = [];
$search_performed = false;

// Perform search if query is provided
if (!empty($query)) {
    $searchEngine = new SearchEngine();
    $results = $searchEngine->globalSearch($query, $filters, 100);
    $search_performed = true;
}

// Get filter manager for saved filters
$filterManager = new FilterManager();
$savedFilters = $filterManager->getUserFilters($current_user['id']);
$filterPresets = $filterManager->getFilterPresets();

// Get available sales representatives for filter
require_once '../includes/order_functions.php';
$sales_reps = getSalesRepresentatives();
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pokročilé vyhledávání - CIG Realizace</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/search.css">
    <link rel="stylesheet" href="../assets/css/orders.css">
</head>
<body>
    <div class="container-fluid">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="../dashboard.php">
                    <i class="fas fa-search me-2"></i>Pokročilé vyhledávání
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" href="../dashboard.php">
                        <i class="fas fa-home me-1"></i>Dashboard
                    </a>
                    <a class="nav-link" href="../orders/index.php">
                        <i class="fas fa-list me-1"></i>Objednávky
                    </a>
                </div>
            </div>
        </nav>

        <div class="row">
            <!-- Search and Filters Sidebar -->
            <div class="col-lg-4 col-xl-3">
                <!-- Global Search -->
                <div class="global-search">
                    <form method="GET" action="">
                        <div class="search-input-group">
                            <input type="text" 
                                   id="global-search" 
                                   name="q"
                                   value="<?= htmlspecialchars($query) ?>"
                                   placeholder="Vyhledávání objednávek, katalogů, technologií..."
                                   autocomplete="off">
                            <button type="submit" id="search-btn" class="btn">
                                <i class="fas fa-search"></i>
                            </button>
                            <button type="button" id="advanced-search-btn" class="btn">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                        <div id="search-suggestions" class="search-suggestions"></div>
                    </form>
                </div>

                <!-- Advanced Filters -->
                <div class="advanced-filters" id="advanced-filters-panel">
                    <div class="filter-header">
                        <h5><i class="fas fa-filter me-2"></i>Pokročilé filtry</h5>
                        <div class="filter-actions">
                            <button type="button" id="clear-filters" class="btn">
                                <i class="fas fa-times me-1"></i>Vymazat
                            </button>
                            <button type="button" id="save-filter" class="btn">
                                <i class="fas fa-save me-1"></i>Uložit
                            </button>
                        </div>
                    </div>
                    
                    <div class="filter-content">
                        <form method="GET" id="advanced-filters-form">
                            <input type="hidden" name="q" value="<?= htmlspecialchars($query) ?>">
                            
                            <div class="filter-groups">
                                <!-- Sales Representative Filter -->
                                <div class="filter-group">
                                    <label for="sales_rep">Obchodník:</label>
                                    <select name="sales_rep" id="sales_rep" class="form-select">
                                        <option value="">Všichni obchodníci</option>
                                        <?php foreach ($sales_reps as $rep): ?>
                                        <option value="<?= htmlspecialchars($rep['sales_rep']) ?>" 
                                                <?= $filters['sales_rep'] === $rep['sales_rep'] ? 'selected' : '' ?>>
                                            <?= htmlspecialchars($rep['sales_rep_name']) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <!-- Preview Status Filter -->
                                <div class="filter-group">
                                    <label>Stav náhledu:</label>
                                    <div class="checkbox-group">
                                        <label>
                                            <input type="checkbox" name="preview_status[]" value="not_created"
                                                   <?= in_array('not_created', (array)$filters['preview_status']) ? 'checked' : '' ?>>
                                            Nevytvořen
                                        </label>
                                        <label>
                                            <input type="checkbox" name="preview_status[]" value="sent_to_client"
                                                   <?= in_array('sent_to_client', (array)$filters['preview_status']) ? 'checked' : '' ?>>
                                            Odeslán klientovi
                                        </label>
                                        <label>
                                            <input type="checkbox" name="preview_status[]" value="approved"
                                                   <?= in_array('approved', (array)$filters['preview_status']) ? 'checked' : '' ?>>
                                            Schválen
                                        </label>
                                    </div>
                                </div>

                                <!-- Date Range Filter -->
                                <div class="filter-group">
                                    <label>Období objednávky:</label>
                                    <div class="date-range">
                                        <input type="date" name="date_from" id="date_from" 
                                               value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>"
                                               placeholder="Od">
                                        <input type="date" name="date_to" id="date_to" 
                                               value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>"
                                               placeholder="Do">
                                    </div>
                                </div>

                                <!-- Technology Filter -->
                                <div class="filter-group">
                                    <label for="technology-filter">Technologie:</label>
                                    <div class="technology-filter-container">
                                        <input type="text" name="technology" id="technology-filter" 
                                               class="form-control"
                                               value="<?= htmlspecialchars($filters['technology'] ?? '') ?>"
                                               placeholder="Vyhledávání technologií...">
                                        <div id="technology-suggestions" class="technology-suggestions"></div>
                                    </div>
                                </div>

                                <!-- Inventory Status Filter -->
                                <div class="filter-group">
                                    <label>Stav zásob:</label>
                                    <div class="checkbox-group">
                                        <label>
                                            <input type="checkbox" name="inventory_status[]" value="not_in_stock"
                                                   <?= in_array('not_in_stock', (array)$filters['inventory_status']) ? 'checked' : '' ?>>
                                            Není skladem
                                        </label>
                                        <label>
                                            <input type="checkbox" name="inventory_status[]" value="ordered"
                                                   <?= in_array('ordered', (array)$filters['inventory_status']) ? 'checked' : '' ?>>
                                            Objednáno
                                        </label>
                                        <label>
                                            <input type="checkbox" name="inventory_status[]" value="in_stock"
                                                   <?= in_array('in_stock', (array)$filters['inventory_status']) ? 'checked' : '' ?>>
                                            Skladem
                                        </label>
                                    </div>
                                </div>

                                <!-- Special Filters -->
                                <div class="filter-group">
                                    <label>Speciální filtry:</label>
                                    <div class="checkbox-group">
                                        <label>
                                            <input type="checkbox" name="is_completed" value="1"
                                                   <?= $filters['is_completed'] === true ? 'checked' : '' ?>>
                                            Pouze dokončené
                                        </label>
                                        <label>
                                            <input type="checkbox" name="overdue_only" value="1"
                                                   <?= $filters['overdue_only'] === true ? 'checked' : '' ?>>
                                            Pouze zpožděné
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search me-2"></i>Aplikovat filtry
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Saved Filters -->
                <?php if (!empty($savedFilters) || !empty($filterPresets)): ?>
                <div class="saved-filters">
                    <div class="saved-filters-header">
                        <i class="fas fa-bookmark me-2"></i>Uložené filtry
                    </div>
                    <div class="saved-filters-content">
                        <?php if (!empty($savedFilters)): ?>
                        <div class="filter-list">
                            <h6>Moje filtry:</h6>
                            <div id="saved-filters-list">
                                <?php foreach ($savedFilters as $filter): ?>
                                <div class="filter-item">
                                    <span class="filter-name"><?= htmlspecialchars($filter['name']) ?></span>
                                    <div class="filter-item-actions">
                                        <button class="btn btn-sm btn-outline-primary load-filter" data-id="<?= $filter['id'] ?>">
                                            <i class="fas fa-play"></i>
                                        </button>
                                        <?php if ($filter['user_id'] == $current_user['id']): ?>
                                        <button class="btn btn-sm btn-outline-danger delete-filter" data-id="<?= $filter['id'] ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($filterPresets)): ?>
                        <div class="quick-presets">
                            <h6>Rychlé předvolby:</h6>
                            <div class="preset-buttons" id="filter-presets">
                                <?php foreach ($filterPresets as $preset): ?>
                                <button class="preset-btn" data-filters='<?= htmlspecialchars(json_encode($preset['filters'])) ?>'>
                                    <?= htmlspecialchars($preset['description']) ?>
                                </button>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Search Results -->
            <div class="col-lg-8 col-xl-9">
                <?php if ($search_performed): ?>
                    <!-- Search Results Header -->
                    <div class="search-results-header mb-4">
                        <h4>
                            <i class="fas fa-search-plus me-2"></i>
                            Výsledky vyhledávání pro: <strong>"<?= htmlspecialchars($query) ?>"</strong>
                        </h4>
                        <p class="text-muted">
                            Celkem nalezeno: <strong><?= $results['total_results'] ?></strong> výsledků
                        </p>
                        
                        <!-- Export Buttons -->
                        <?php if ($results['total_results'] > 0): ?>
                        <div class="export-buttons">
                            <a href="../ajax/export_filtered.php?<?= http_build_query(array_merge(['q' => $query], $filters)) ?>&format=csv" 
                               class="export-btn" target="_blank">
                                <i class="fas fa-file-csv"></i>Export CSV
                            </a>
                            <a href="../ajax/export_filtered.php?<?= http_build_query(array_merge(['q' => $query], $filters)) ?>&format=excel" 
                               class="export-btn" target="_blank">
                                <i class="fas fa-file-excel"></i>Export Excel
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Orders Results -->
                    <?php if (!empty($results['orders'])): ?>
                    <div class="search-results-section mb-4">
                        <h5 class="section-title">
                            <i class="fas fa-shopping-cart me-2"></i>
                            Objednávky (<?= count($results['orders']) ?>)
                        </h5>
                        <div class="results-grid">
                            <?php foreach ($results['orders'] as $order): ?>
                            <div class="result-card order-card">
                                <div class="card-header">
                                    <h6>
                                        <a href="../orders/detail.php?id=<?= $order['id'] ?>" class="text-decoration-none">
                                            <?= htmlspecialchars($order['order_code']) ?>
                                        </a>
                                    </h6>
                                    <span class="badge bg-primary"><?= htmlspecialchars($order['sales_rep_name']) ?></span>
                                </div>
                                <div class="card-body">
                                    <p class="mb-1">
                                        <strong>Datum:</strong> <?= date('d.m.Y', strtotime($order['order_date'])) ?>
                                    </p>
                                    <p class="mb-1">
                                        <strong>Stav náhledu:</strong> 
                                        <?php $status = formatPreviewStatus($order['preview_status']); ?>
                                        <span class="badge <?= $status['class'] ?>"><?= $status['label'] ?></span>
                                    </p>
                                    <p class="mb-0">
                                        <strong>Položek:</strong> <?= $order['items_count'] ?>
                                    </p>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Order Items Results -->
                    <?php if (!empty($results['items'])): ?>
                    <div class="search-results-section mb-4">
                        <h5 class="section-title">
                            <i class="fas fa-list me-2"></i>
                            Položky objednávek (<?= count($results['items']) ?>)
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Objednávka</th>
                                        <th>Katalog</th>
                                        <th>Množství</th>
                                        <th>Technologie</th>
                                        <th>Obchodník</th>
                                        <th>Akce</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($results['items'] as $item): ?>
                                    <tr>
                                        <td>
                                            <a href="../orders/detail.php?id=<?= $item['order_id'] ?>" class="text-decoration-none">
                                                <?= htmlspecialchars($item['order_code']) ?>
                                            </a>
                                        </td>
                                        <td><strong><?= htmlspecialchars($item['catalog_code']) ?></strong></td>
                                        <td><?= number_format($item['quantity']) ?></td>
                                        <td><?= htmlspecialchars($item['technology_assignment'] ?? '-') ?></td>
                                        <td><?= htmlspecialchars($item['sales_rep_name']) ?></td>
                                        <td>
                                            <a href="../orders/detail.php?id=<?= $item['order_id'] ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> Detail
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Catalog Codes Results -->
                    <?php if (!empty($results['catalog_codes'])): ?>
                    <div class="search-results-section mb-4">
                        <h5 class="section-title">
                            <i class="fas fa-barcode me-2"></i>
                            Katalogové kódy (<?= count($results['catalog_codes']) ?>)
                        </h5>
                        <div class="catalog-results">
                            <?php foreach ($results['catalog_codes'] as $catalog): ?>
                            <div class="catalog-item">
                                <strong><?= htmlspecialchars($catalog['catalog_code']) ?></strong>
                                <span class="text-muted">- použito <?= $catalog['usage_count'] ?>x</span>
                                <button class="btn btn-sm btn-outline-primary ms-2" 
                                        onclick="searchByCatalog('<?= htmlspecialchars($catalog['catalog_code']) ?>')">
                                    <i class="fas fa-search"></i> Vyhledat
                                </button>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Technologies Results -->
                    <?php if (!empty($results['technologies'])): ?>
                    <div class="search-results-section mb-4">
                        <h5 class="section-title">
                            <i class="fas fa-cogs me-2"></i>
                            Technologie (<?= count($results['technologies']) ?>)
                        </h5>
                        <div class="technology-results">
                            <?php foreach ($results['technologies'] as $tech): ?>
                            <div class="technology-item">
                                <strong><?= htmlspecialchars($tech['technology']) ?></strong>
                                <span class="text-muted">- použito <?= $tech['usage_count'] ?>x</span>
                                <button class="btn btn-sm btn-outline-primary ms-2" 
                                        onclick="searchByTechnology('<?= htmlspecialchars($tech['technology']) ?>')">
                                    <i class="fas fa-search"></i> Vyhledat
                                </button>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if ($results['total_results'] === 0): ?>
                    <div class="empty-results">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4>Žádné výsledky</h4>
                            <p class="text-muted">Pro zadané kritéria nebyly nalezeny žádné výsledky.</p>
                            <button class="btn btn-primary" onclick="clearSearch()">
                                <i class="fas fa-times me-2"></i>Vymazat vyhledávání
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>

                <?php else: ?>
                    <!-- Welcome Screen -->
                    <div class="welcome-screen">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-4x text-primary mb-4"></i>
                            <h2>Pokročilé vyhledávání</h2>
                            <p class="lead text-muted mb-4">
                                Vyhledávejte v objednávkách, katalogových kódech a technologiích
                            </p>
                            <div class="search-tips">
                                <h5>Tipy pro vyhledávání:</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Použijte minimálně 2 znaky</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Kombinujte filtry pro přesnější výsledky</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Uložte si často používané filtry</li>
                                    <li><i class="fas fa-lightbulb text-warning me-2"></i>Exportujte výsledky do CSV nebo Excel</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="../assets/js/search.js"></script>
    
    <script>
        // Helper functions for search buttons
        function searchByCatalog(catalogCode) {
            const url = new URL(window.location);
            url.searchParams.set('q', catalogCode);
            window.location.href = url.toString();
        }
        
        function searchByTechnology(technology) {
            const url = new URL(window.location);
            url.searchParams.set('technology', technology);
            window.location.href = url.toString();
        }
        
        function clearSearch() {
            window.location.href = window.location.pathname;
        }
    </script>
</body>
</html>
