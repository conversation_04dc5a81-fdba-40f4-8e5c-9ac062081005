#!/bin/bash

# CIG Realizace - Production Deployment Script
# Automated deployment script for production environment

set -e  # Exit on any error

# Configuration
PROJECT_NAME="cig-realizace"
BACKUP_DIR="/var/backups/$PROJECT_NAME"
DEPLOY_DIR="/var/www/$PROJECT_NAME"
TEMP_DIR="/tmp/$PROJECT_NAME-deploy"
LOG_FILE="/var/log/$PROJECT_NAME-deploy.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root or with sudo
check_permissions() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root or with sudo"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check PHP version
    if ! command -v php &> /dev/null; then
        error "PHP is not installed"
    fi
    
    PHP_VERSION=$(php -r "echo PHP_VERSION;")
    if [[ $(echo "$PHP_VERSION 8.3" | awk '{print ($1 >= $2)}') -eq 0 ]]; then
        error "PHP 8.3 or higher is required. Current version: $PHP_VERSION"
    fi
    
    # Check required PHP extensions
    REQUIRED_EXTENSIONS=("pdo" "pdo_mysql" "pdo_sqlite" "mbstring" "openssl" "curl" "json")
    for ext in "${REQUIRED_EXTENSIONS[@]}"; do
        if ! php -m | grep -q "^$ext$"; then
            error "Required PHP extension '$ext' is not installed"
        fi
    done
    
    # Check MySQL/MariaDB
    if ! command -v mysql &> /dev/null; then
        warning "MySQL/MariaDB client not found. Database operations may fail."
    fi
    
    # Check Apache
    if ! command -v apache2ctl &> /dev/null && ! command -v httpd &> /dev/null; then
        warning "Apache web server not found"
    fi
    
    success "System requirements check passed"
}

# Create backup of current installation
create_backup() {
    log "Creating backup of current installation..."
    
    if [[ ! -d "$DEPLOY_DIR" ]]; then
        log "No existing installation found, skipping backup"
        return
    fi
    
    # Create backup directory
    mkdir -p "$BACKUP_DIR"
    
    # Create timestamped backup
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_NAME"
    
    # Backup files
    log "Backing up files to $BACKUP_PATH..."
    cp -r "$DEPLOY_DIR" "$BACKUP_PATH"
    
    # Backup database
    if [[ -f "$DEPLOY_DIR/config/database.php" ]]; then
        log "Backing up database..."
        
        # Extract database credentials (simplified - in production, use proper config parsing)
        DB_NAME=$(grep -o "dbname.*" "$DEPLOY_DIR/config/database.php" | head -1 | cut -d"'" -f4)
        DB_USER=$(grep -o "username.*" "$DEPLOY_DIR/config/database.php" | head -1 | cut -d"'" -f4)
        DB_PASS=$(grep -o "password.*" "$DEPLOY_DIR/config/database.php" | head -1 | cut -d"'" -f4)
        
        if [[ -n "$DB_NAME" && -n "$DB_USER" ]]; then
            mysqldump -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_PATH/database.sql" 2>/dev/null || warning "Database backup failed"
        fi
    fi
    
    # Compress backup
    tar -czf "$BACKUP_PATH.tar.gz" -C "$BACKUP_DIR" "$BACKUP_NAME"
    rm -rf "$BACKUP_PATH"
    
    # Keep only last 10 backups
    cd "$BACKUP_DIR"
    ls -t *.tar.gz | tail -n +11 | xargs -r rm --
    
    success "Backup created: $BACKUP_PATH.tar.gz"
}

# Deploy application files
deploy_files() {
    log "Deploying application files..."
    
    # Create temporary directory
    rm -rf "$TEMP_DIR"
    mkdir -p "$TEMP_DIR"
    
    # Copy source files (assuming script is run from project root)
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
    
    log "Copying files from $PROJECT_ROOT to $TEMP_DIR..."
    cp -r "$PROJECT_ROOT"/* "$TEMP_DIR/"
    
    # Remove development files
    rm -rf "$TEMP_DIR/tests"
    rm -rf "$TEMP_DIR/.git"
    rm -f "$TEMP_DIR"/.gitignore
    rm -f "$TEMP_DIR"/README.md
    rm -f "$TEMP_DIR"/PROGRESS.md
    rm -f "$TEMP_DIR"/*.md
    
    # Create production directories
    mkdir -p "$TEMP_DIR/logs"
    mkdir -p "$TEMP_DIR/uploads"
    mkdir -p "$TEMP_DIR/cache"
    
    # Set proper permissions
    find "$TEMP_DIR" -type f -exec chmod 644 {} \;
    find "$TEMP_DIR" -type d -exec chmod 755 {} \;
    chmod 755 "$TEMP_DIR/deploy"/*.sh
    chmod 755 "$TEMP_DIR/cron"/*.php
    chmod 755 "$TEMP_DIR"/*.php
    
    # Make writable directories
    chmod 777 "$TEMP_DIR/logs"
    chmod 777 "$TEMP_DIR/uploads"
    chmod 777 "$TEMP_DIR/cache"
    chmod 777 "$TEMP_DIR/data" 2>/dev/null || true
    
    # Create deployment directory
    mkdir -p "$DEPLOY_DIR"
    
    # Stop web server temporarily
    log "Stopping web server..."
    systemctl stop apache2 2>/dev/null || systemctl stop httpd 2>/dev/null || warning "Could not stop web server"
    
    # Deploy files
    log "Moving files to production directory..."
    rsync -av --delete "$TEMP_DIR/" "$DEPLOY_DIR/"
    
    # Set ownership
    chown -R www-data:www-data "$DEPLOY_DIR" 2>/dev/null || chown -R apache:apache "$DEPLOY_DIR" 2>/dev/null || warning "Could not set file ownership"
    
    success "Files deployed successfully"
}

# Configure environment
configure_environment() {
    log "Configuring production environment..."
    
    # Create environment file if it doesn't exist
    ENV_FILE="$DEPLOY_DIR/.env"
    if [[ ! -f "$ENV_FILE" ]]; then
        log "Creating environment configuration file..."
        cat > "$ENV_FILE" << EOF
# CIG Realizace Production Environment Configuration
DB_HOST=localhost
DB_NAME=cig_realizace_prod
DB_USERNAME=cig_user
DB_PASSWORD=
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>
REPLY_TO_EMAIL=<EMAIL>
EOF
        chmod 600 "$ENV_FILE"
        warning "Please edit $ENV_FILE with your production credentials"
    fi
    
    # Create Apache virtual host configuration
    VHOST_FILE="/etc/apache2/sites-available/$PROJECT_NAME.conf"
    if [[ ! -f "$VHOST_FILE" ]]; then
        log "Creating Apache virtual host configuration..."
        cat > "$VHOST_FILE" << EOF
<VirtualHost *:80>
    ServerName cigimage.cz
    ServerAlias www.cigimage.cz
    DocumentRoot $DEPLOY_DIR
    
    <Directory $DEPLOY_DIR>
        AllowOverride All
        Require all granted
        
        # Security headers
        Header always set X-Content-Type-Options nosniff
        Header always set X-Frame-Options DENY
        Header always set X-XSS-Protection "1; mode=block"
        Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    </Directory>
    
    # Deny access to sensitive files
    <FilesMatch "\.(env|log|sql|md)$">
        Require all denied
    </FilesMatch>
    
    <Directory $DEPLOY_DIR/config>
        Require all denied
    </Directory>
    
    <Directory $DEPLOY_DIR/logs>
        Require all denied
    </Directory>
    
    <Directory $DEPLOY_DIR/data>
        Require all denied
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/$PROJECT_NAME-error.log
    CustomLog \${APACHE_LOG_DIR}/$PROJECT_NAME-access.log combined
</VirtualHost>

# SSL Virtual Host (if SSL certificate is available)
<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerName cigimage.cz
    ServerAlias www.cigimage.cz
    DocumentRoot $DEPLOY_DIR
    
    SSLEngine on
    SSLCertificateFile /etc/ssl/certs/cigimage.cz.crt
    SSLCertificateKeyFile /etc/ssl/private/cigimage.cz.key
    
    <Directory $DEPLOY_DIR>
        AllowOverride All
        Require all granted
    </Directory>
    
    # Same security settings as HTTP
    <FilesMatch "\.(env|log|sql|md)$">
        Require all denied
    </FilesMatch>
    
    <Directory $DEPLOY_DIR/config>
        Require all denied
    </Directory>
    
    <Directory $DEPLOY_DIR/logs>
        Require all denied
    </Directory>
    
    <Directory $DEPLOY_DIR/data>
        Require all denied
    </Directory>
    
    ErrorLog \${APACHE_LOG_DIR}/$PROJECT_NAME-ssl-error.log
    CustomLog \${APACHE_LOG_DIR}/$PROJECT_NAME-ssl-access.log combined
</VirtualHost>
</IfModule>
EOF
        
        # Enable site
        a2ensite "$PROJECT_NAME" 2>/dev/null || warning "Could not enable Apache site"
        a2enmod rewrite headers ssl 2>/dev/null || warning "Could not enable Apache modules"
    fi
    
    success "Environment configured"
}

# Setup database
setup_database() {
    log "Setting up production database..."
    
    # Source environment variables
    if [[ -f "$DEPLOY_DIR/.env" ]]; then
        source "$DEPLOY_DIR/.env"
    fi
    
    if [[ -z "$DB_USERNAME" || -z "$DB_PASSWORD" ]]; then
        warning "Database credentials not configured. Please edit $DEPLOY_DIR/.env"
        return
    fi
    
    # Test database connection
    if mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log "Database connection successful"
    else
        log "Creating database and user..."
        
        # This would typically require root MySQL access
        warning "Please create database manually:"
        echo "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        echo "CREATE USER '$DB_USERNAME'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
        echo "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USERNAME'@'localhost';"
        echo "FLUSH PRIVILEGES;"
        return
    fi
    
    # Run database migrations
    log "Running database migrations..."
    cd "$DEPLOY_DIR"
    
    for sql_file in sql/*.sql; do
        if [[ -f "$sql_file" ]]; then
            log "Executing $sql_file..."
            mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_NAME" < "$sql_file" || warning "Failed to execute $sql_file"
        fi
    done
    
    success "Database setup completed"
}

# Setup cron jobs
setup_cron() {
    log "Setting up cron jobs..."
    
    # Create cron job for email notifications
    CRON_FILE="/etc/cron.d/$PROJECT_NAME"
    cat > "$CRON_FILE" << EOF
# CIG Realizace Cron Jobs
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# Process email notifications every 5 minutes
*/5 * * * * www-data cd $DEPLOY_DIR && php cron/send_notifications.php >> logs/email_cron.log 2>&1

# Daily reports and cleanup at 6:00 AM
0 6 * * * www-data cd $DEPLOY_DIR && php cron/daily_reports.php >> logs/daily_cron.log 2>&1

# Weekly backup on Sunday at 2:00 AM
0 2 * * 0 root $DEPLOY_DIR/deploy/backup.sh >> logs/backup.log 2>&1
EOF
    
    chmod 644 "$CRON_FILE"
    
    success "Cron jobs configured"
}

# Start services
start_services() {
    log "Starting services..."
    
    # Start web server
    systemctl start apache2 2>/dev/null || systemctl start httpd 2>/dev/null || warning "Could not start web server"
    
    # Reload cron
    systemctl reload cron 2>/dev/null || systemctl reload crond 2>/dev/null || warning "Could not reload cron"
    
    success "Services started"
}

# Run post-deployment tests
run_tests() {
    log "Running post-deployment tests..."
    
    # Test web server response
    if command -v curl &> /dev/null; then
        HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/ || echo "000")
        if [[ "$HTTP_CODE" == "200" ]]; then
            success "Web server responding correctly"
        else
            warning "Web server returned HTTP $HTTP_CODE"
        fi
    fi
    
    # Test database connection
    cd "$DEPLOY_DIR"
    if php -r "
        try {
            require 'config/database.php';
            echo 'Database connection successful\n';
        } catch (Exception \$e) {
            echo 'Database connection failed: ' . \$e->getMessage() . '\n';
            exit(1);
        }
    " 2>/dev/null; then
        success "Database connection test passed"
    else
        warning "Database connection test failed"
    fi
    
    success "Post-deployment tests completed"
}

# Cleanup
cleanup() {
    log "Cleaning up temporary files..."
    rm -rf "$TEMP_DIR"
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "Starting deployment of $PROJECT_NAME..."
    
    check_permissions
    check_requirements
    create_backup
    deploy_files
    configure_environment
    setup_database
    setup_cron
    start_services
    run_tests
    cleanup
    
    success "Deployment completed successfully!"
    log "Application is now available at: http://$(hostname -f)/"
    log "Please review the configuration files and update credentials as needed."
}

# Run main function
main "$@"
