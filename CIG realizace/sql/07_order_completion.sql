-- CIG Realizace - Phase 08: Add order completion columns
-- Add columns for tracking order completion

-- Add completed_date column if it doesn't exist
ALTER TABLE orders ADD COLUMN completed_date DATETIME NULL DEFAULT NULL;

-- Add completed_by column if it doesn't exist  
ALTER TABLE orders ADD COLUMN completed_by INTEGER NULL DEFAULT NULL;

-- Add foreign key constraint for completed_by (if supported)
-- Note: SQLite doesn't support adding foreign key constraints to existing tables
-- This would need to be done during table creation

-- Create index for completed orders
CREATE INDEX IF NOT EXISTS idx_orders_completed ON orders(is_completed, completed_date);
CREATE INDEX IF NOT EXISTS idx_orders_completed_by ON orders(completed_by);
