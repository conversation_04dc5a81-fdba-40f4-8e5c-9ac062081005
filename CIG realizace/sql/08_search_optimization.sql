-- Phase 09 - Search and Filter Optimization
-- CIG Realizace - Database enhancements for advanced search functionality

-- Add full-text search indexes for better search performance
ALTER TABLE orders ADD FULLTEXT(order_code);
ALTER TABLE order_items ADD FULLTEXT(catalog_code, technology_assignment);

-- Create composite indexes for filtering optimization
CREATE INDEX idx_orders_status_date ON orders(preview_status, order_date);
CREATE INDEX idx_orders_sales_rep_date ON orders(sales_rep, order_date);
CREATE INDEX idx_orders_completed_date ON orders(is_completed, order_date);
CREATE INDEX idx_items_technology ON order_items(technology_assignment);
CREATE INDEX idx_items_inventory ON order_items(inventory_status);
CREATE INDEX idx_items_relevance ON order_items(is_relevant);

-- Create table for saved filters
CREATE TABLE saved_filters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    filters JSON NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_filter (user_id, name)
);

-- Create table for search analytics
CREATE TABLE search_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    search_query VARCHAR(255),
    search_type ENUM('global', 'orders', 'catalog', 'technology') DEFAULT 'global',
    results_count INT DEFAULT 0,
    execution_time_ms INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_search_query (search_query),
    INDEX idx_search_date (created_at),
    INDEX idx_user_searches (user_id, created_at)
);

-- Create table for quick filter presets
CREATE TABLE filter_presets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    filters JSON NOT NULL,
    is_system BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_preset_name (name)
);

-- Insert default filter presets
INSERT INTO filter_presets (name, description, filters, is_system, sort_order) VALUES
('today_orders', 'Dnešní objednávky', '{"date_from": "today", "date_to": "today"}', TRUE, 1),
('this_week', 'Objednávky tohoto týdne', '{"date_from": "week_start", "date_to": "week_end"}', TRUE, 2),
('pending_previews', 'Čekající náhledy', '{"preview_status": ["not_created", "sent_to_client"]}', TRUE, 3),
('approved_orders', 'Schválené objednávky', '{"preview_status": ["approved"]}', TRUE, 4),
('overdue_orders', 'Zpožděné objednávky', '{"overdue_only": true}', TRUE, 5),
('completed_orders', 'Dokončené objednávky', '{"is_completed": true}', TRUE, 6);

-- Add search performance tracking columns to existing tables
ALTER TABLE orders ADD COLUMN search_vector TEXT GENERATED ALWAYS AS (
    CONCAT_WS(' ', 
        order_code, 
        sales_rep_name, 
        IFNULL(customer_name, ''),
        IFNULL(notes, '')
    )
) STORED;

-- Create index on search vector
CREATE FULLTEXT INDEX idx_orders_search_vector ON orders(search_vector);

-- Add search optimization for order items
ALTER TABLE order_items ADD COLUMN search_vector TEXT GENERATED ALWAYS AS (
    CONCAT_WS(' ', 
        catalog_code, 
        IFNULL(technology_assignment, ''),
        IFNULL(notes, '')
    )
) STORED;

-- Create index on order items search vector
CREATE FULLTEXT INDEX idx_items_search_vector ON order_items(search_vector);
