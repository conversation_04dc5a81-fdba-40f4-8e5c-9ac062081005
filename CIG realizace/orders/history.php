<?php
/**
 * Order History Page
 * CIG Realizace - Phase 06
 */

require_once '../includes/auth_check.php';
require_once '../classes/HistoryLogger.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    header('Location: ../index.php');
    exit;
}

// Get order ID from URL
$order_id = intval($_GET['id'] ?? 0);
if (!$order_id) {
    header('Location: index.php');
    exit;
}

try {
    $pdo = getDbConnection();
    
    // Get order details
    $stmt = $pdo->prepare("
        SELECT o.*, u.username as sales_rep_username, u.full_name as sales_rep_name
        FROM orders o
        LEFT JOIN users u ON o.sales_rep_id = u.id
        WHERE o.id = ?
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        throw new Exception('Objednávka nebyla nalezena');
    }
    
    // Check permissions
    if ($current_user['role'] !== 'admin' && 
        $current_user['role'] !== 'obchodnik' && 
        $order['sales_rep_id'] != $current_user['id']) {
        header('Location: ../includes/access_denied.php');
        exit;
    }
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}

$page_title = "Historie objednávky {$order['order_code']}";
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/history.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-industry me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="index.php">
                    <i class="fas fa-list me-1"></i>Objednávky
                </a>
                <a class="nav-link" href="detail.php?id=<?php echo $order_id; ?>">
                    <i class="fas fa-eye me-1"></i>Detail objednávky
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php else: ?>
            <!-- Order Header -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="fas fa-history me-2"></i>Historie objednávky
                            </h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5 class="text-primary"><?php echo htmlspecialchars($order['order_code']); ?></h5>
                                    <p class="mb-1">
                                        <strong>Obchodník:</strong> 
                                        <?php echo htmlspecialchars($order['sales_rep_name'] ?: $order['sales_rep']); ?>
                                    </p>
                                    <p class="mb-1">
                                        <strong>Datum objednávky:</strong> 
                                        <?php echo date('d.m.Y', strtotime($order['order_date'])); ?>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1">
                                        <strong>Stav náhledu:</strong>
                                        <span class="badge bg-<?php echo getPreviewStatusColor($order['preview_status']); ?>">
                                            <?php echo getPreviewStatusLabel($order['preview_status']); ?>
                                        </span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>Stav objednávky:</strong>
                                        <span class="badge bg-<?php echo getOrderStatusColor($order['status']); ?>">
                                            <?php echo getOrderStatusLabel($order['status']); ?>
                                        </span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History Filters -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="actionTypeFilter" class="form-label">Typ akce:</label>
                                    <select id="actionTypeFilter" class="form-select">
                                        <option value="">Všechny akce</option>
                                        <option value="preview_status_change">Změny stavu náhledu</option>
                                        <option value="delivery_date_change">Změny termínu dodání</option>
                                        <option value="technology_assignment">Přiřazení technologie</option>
                                        <option value="item_relevance_change">Změny relevance položek</option>
                                        <option value="inventory_status_change">Změny stavu zásob</option>
                                        <option value="order_completion">Dokončení objednávky</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="limitFilter" class="form-label">Počet záznamů:</label>
                                    <select id="limitFilter" class="form-select">
                                        <option value="25">25 záznamů</option>
                                        <option value="50" selected>50 záznamů</option>
                                        <option value="100">100 záznamů</option>
                                        <option value="0">Všechny záznamy</option>
                                    </select>
                                </div>
                                <div class="col-md-4 d-flex align-items-end">
                                    <button id="refreshHistory" class="btn btn-primary">
                                        <i class="fas fa-sync-alt me-1"></i>Obnovit
                                    </button>
                                    <button id="exportHistory" class="btn btn-outline-secondary ms-2">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History Timeline -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-timeline me-2"></i>Timeline změn
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="historyLoading" class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Načítání...</span>
                                </div>
                                <p class="mt-2">Načítání historie...</p>
                            </div>
                            <div id="historyContainer" class="history-timeline" style="display: none;">
                                <!-- History entries will be loaded here -->
                            </div>
                            <div id="historyError" class="alert alert-warning" style="display: none;">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <span id="historyErrorMessage">Chyba při načítání historie</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="../assets/js/history.js"></script>
    <script>
        // Initialize history page
        $(document).ready(function() {
            const orderId = <?php echo $order_id; ?>;
            HistoryManager.init(orderId);
        });
    </script>
</body>
</html>

<?php
function getPreviewStatusLabel($status) {
    $labels = [
        'not_created' => 'Nevytvořen',
        'sent_to_client' => 'Odeslán klientovi',
        'approved' => 'Schválen'
    ];
    return $labels[$status] ?? $status;
}

function getPreviewStatusColor($status) {
    $colors = [
        'not_created' => 'secondary',
        'sent_to_client' => 'warning',
        'approved' => 'success'
    ];
    return $colors[$status] ?? 'secondary';
}

function getOrderStatusLabel($status) {
    $labels = [
        'pending' => 'Čekající',
        'in_progress' => 'Probíhá',
        'completed' => 'Dokončeno',
        'cancelled' => 'Zrušeno'
    ];
    return $labels[$status] ?? $status;
}

function getOrderStatusColor($status) {
    $colors = [
        'pending' => 'warning',
        'in_progress' => 'info',
        'completed' => 'success',
        'cancelled' => 'danger'
    ];
    return $colors[$status] ?? 'secondary';
}
?>
