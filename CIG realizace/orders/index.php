<?php
// Include authentication check
require_once '../includes/auth_check.php';
require_once '../includes/order_functions.php';

// Get current user
$current_user = getCurrentUser();
$user_role = $current_user['role'];

// Get parameters
$page = max(1, intval($_GET['page'] ?? 1));
$sort_by = $_GET['sort'] ?? 'created_at';
$sort_order = $_GET['order'] ?? 'DESC';
$per_page = 25;

// Get filters
$filters = [
    'sales_rep' => $_GET['sales_rep'] ?? '',
    'status' => $_GET['status'] ?? '',
    'preview_status' => $_GET['preview_status'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'search' => $_GET['search'] ?? '',
    'is_completed' => isset($_GET['is_completed']) ? ($_GET['is_completed'] === '1') : null
];

// For non-admin users, filter by their sales rep
if ($user_role === 'obchodnik') {
    // Get user's sales rep prefix - this would need proper mapping
    // For now, we'll show all orders - this can be refined later
}

// Get all orders
$result = getAllOrders($page, $per_page, $sort_by, $sort_order, $filters);
$orders = $result['orders'];
$pagination = $result['pagination'];

// Get statistics
$stats = getOrderStatistics();

// Get available sales representatives for filter
$sales_reps = getSalesRepresentatives();

// Build current URL for pagination
$current_params = $_GET;
unset($current_params['page']);
$base_url = 'index.php';

// Page title based on user role
$page_title = $user_role === 'admin' ? 'Všechny objednávky' : 'Moje objednávky';
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/orders.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard.php">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($current_user['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user-edit me-2"></i>Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="orders-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                            <li class="breadcrumb-item active"><?= $page_title ?></li>
                        </ol>
                    </nav>
                    <h1><i class="fas fa-list-alt me-2"></i><?= $page_title ?></h1>
                    <p class="mb-0">Kompletní přehled <?= $user_role === 'admin' ? 'všech' : 'vašich' ?> objednávek v systému</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="pending.php" class="btn btn-outline-light">
                            <i class="fas fa-clock me-1"></i>Čekající objednávky
                        </a>
                        <?php if ($user_role === 'admin'): ?>
                        <a href="../admin/import.php" class="btn btn-light">
                            <i class="fas fa-file-import me-1"></i>Import CSV
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Cards -->
    <div class="container">
        <div class="row stats-cards">
            <div class="col-md-3 mb-4">
                <div class="stat-card">
                    <i class="fas fa-list-alt stat-icon text-primary"></i>
                    <div class="stat-number text-primary"><?= $stats['total'] ?></div>
                    <p class="stat-label">Celkem objednávek</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card">
                    <i class="fas fa-clock stat-icon text-warning"></i>
                    <div class="stat-number text-warning"><?= $stats['pending'] ?></div>
                    <p class="stat-label">Čekající objednávky</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card">
                    <i class="fas fa-cogs stat-icon text-info"></i>
                    <div class="stat-number text-info"><?= $stats['in_progress'] ?></div>
                    <p class="stat-label">Ve zpracování</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stat-card">
                    <i class="fas fa-check-circle stat-icon text-success"></i>
                    <div class="stat-number text-success"><?= $stats['completed'] ?></div>
                    <p class="stat-label">Dokončené</p>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
            <h5><i class="fas fa-filter me-2"></i>Filtry a vyhledávání</h5>
            <form method="GET" id="filtersForm">
                <div class="row filter-row">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Vyhledávání:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" name="search" 
                                   value="<?= htmlspecialchars($filters['search']) ?>" 
                                   placeholder="Kód objednávky, obchodník...">
                            <?php if (!empty($filters['search'])): ?>
                            <button type="button" class="btn btn-outline-secondary" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label for="sales_rep" class="form-label">Obchodník:</label>
                        <select class="form-select" name="sales_rep" id="sales_rep">
                            <option value="">Všichni</option>
                            <?php foreach ($sales_reps as $rep): ?>
                            <option value="<?= htmlspecialchars($rep['sales_rep']) ?>" 
                                    <?= $filters['sales_rep'] === $rep['sales_rep'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($rep['sales_rep_name'] ?: $rep['sales_rep']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Stav objednávky:</label>
                        <select class="form-select" name="status" id="status">
                            <option value="">Všechny</option>
                            <option value="pending" <?= $filters['status'] === 'pending' ? 'selected' : '' ?>>Čeká</option>
                            <option value="in_progress" <?= $filters['status'] === 'in_progress' ? 'selected' : '' ?>>Ve zpracování</option>
                            <option value="completed" <?= $filters['status'] === 'completed' ? 'selected' : '' ?>>Dokončeno</option>
                            <option value="cancelled" <?= $filters['status'] === 'cancelled' ? 'selected' : '' ?>>Zrušeno</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="preview_status" class="form-label">Stav náhledu:</label>
                        <select class="form-select" name="preview_status" id="preview_status">
                            <option value="">Všechny</option>
                            <option value="not_created" <?= $filters['preview_status'] === 'not_created' ? 'selected' : '' ?>>Nevytvořen</option>
                            <option value="sent_to_client" <?= $filters['preview_status'] === 'sent_to_client' ? 'selected' : '' ?>>Odeslán klientovi</option>
                            <option value="approved" <?= $filters['preview_status'] === 'approved' ? 'selected' : '' ?>>Schválen</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label for="is_completed" class="form-label">Dokončené:</label>
                        <select class="form-select" name="is_completed" id="is_completed">
                            <option value="">Všechny</option>
                            <option value="0" <?= $filters['is_completed'] === false ? 'selected' : '' ?>>Ne</option>
                            <option value="1" <?= $filters['is_completed'] === true ? 'selected' : '' ?>>Ano</option>
                        </select>
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-secondary w-100" id="clearFilters">
                            <i class="fas fa-eraser"></i>
                        </button>
                    </div>
                </div>
                <div class="row filter-row">
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">Datum od:</label>
                        <input type="date" class="form-control" name="date_from" id="date_from" 
                               value="<?= htmlspecialchars($filters['date_from']) ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">Datum do:</label>
                        <input type="date" class="form-control" name="date_to" id="date_to" 
                               value="<?= htmlspecialchars($filters['date_to']) ?>">
                    </div>
                </div>
            </form>
        </div>

        <!-- Orders Table -->
        <div class="orders-table-container">
            <?php if (empty($orders)): ?>
            <div class="empty-state">
                <i class="fas fa-inbox"></i>
                <h4>Žádné objednávky</h4>
                <p>Momentálně nejsou k dispozici žádné objednávky odpovídající zadaným kritériím.</p>
                <?php if ($user_role === 'admin'): ?>
                <a href="../admin/import.php" class="btn btn-primary">
                    <i class="fas fa-file-import me-1"></i>Importovat objednávky
                </a>
                <?php endif; ?>
            </div>
            <?php else: ?>
            <table class="table orders-table">
                <thead>
                    <tr>
                        <th class="sortable <?= $sort_by === 'order_code' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="order_code" data-order="<?= strtolower($sort_order) ?>">
                            Kód objednávky
                        </th>
                        <th class="sortable <?= $sort_by === 'sales_rep' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="sales_rep" data-order="<?= strtolower($sort_order) ?>">
                            Obchodník
                        </th>
                        <th class="sortable <?= $sort_by === 'order_date' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="order_date" data-order="<?= strtolower($sort_order) ?>">
                            Datum objednávky
                        </th>
                        <th class="sortable <?= $sort_by === 'status' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="status" data-order="<?= strtolower($sort_order) ?>">
                            Stav objednávky
                        </th>
                        <th class="sortable <?= $sort_by === 'preview_status' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="preview_status" data-order="<?= strtolower($sort_order) ?>">
                            Stav náhledu
                        </th>
                        <th class="sortable <?= $sort_by === 'items_count' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="items_count" data-order="<?= strtolower($sort_order) ?>">
                            Počet položek
                        </th>
                        <th class="sortable <?= $sort_by === 'created_at' ? 'sorted-' . strtolower($sort_order) : '' ?>" 
                            data-column="created_at" data-order="<?= strtolower($sort_order) ?>">
                            Vytvořeno
                        </th>
                        <th>Akce</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): 
                        $order_status = formatOrderStatus($order['status']);
                        $preview_status = formatPreviewStatus($order['preview_status']);
                    ?>
                    <tr class="order-row" data-order-id="<?= $order['id'] ?>">
                        <td>
                            <button type="button" class="btn btn-link order-code-link p-0"
                                    onclick="toggleOrderDetail(<?= $order['id'] ?>)">
                                <i class="fas fa-chevron-right me-1 expand-icon"></i>
                                <?= htmlspecialchars($order['order_code']) ?>
                            </button>
                        </td>
                        <td>
                            <div class="sales-rep-info">
                                <span class="sales-rep-prefix"><?= htmlspecialchars($order['sales_rep']) ?></span>
                                <span class="sales-rep-name"><?= htmlspecialchars($order['sales_rep_name'] ?: 'Neznámý') ?></span>
                            </div>
                        </td>
                        <td><?= date('d.m.Y', strtotime($order['order_date'])) ?></td>
                        <td>
                            <span class="status-badge status-<?= str_replace('_', '-', $order['status']) ?>">
                                <?= $order_status['label'] ?>
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-<?= str_replace('_', '-', $order['preview_status']) ?>">
                                <?= $preview_status['label'] ?>
                            </span>
                        </td>
                        <td>
                            <span class="items-count"><?= $order['items_count'] ?></span>
                        </td>
                        <td><?= date('d.m.Y H:i', strtotime($order['created_at'])) ?></td>
                        <td>
                            <div class="action-buttons">
                                <?php if ($user_role === 'admin' || $user_role === 'grafik'): ?>
                                <button type="button" class="action-btn action-btn-warning"
                                        data-action="status"
                                        data-order-id="<?= $order['id'] ?>"
                                        data-current-status="<?= $order['preview_status'] ?>"
                                        title="Změnit stav">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <!-- Rozbalovací detail objednávky -->
                    <tr class="order-detail-row" id="detail-<?= $order['id'] ?>" style="display: none;">
                        <td colspan="8" class="p-0">
                            <div class="order-detail-container">
                                <div class="loading-placeholder">
                                    <i class="fas fa-spinner fa-spin me-2"></i>Načítám detail objednávky...
                                </div>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Pagination Info -->
            <div class="pagination-info d-flex justify-content-between align-items-center">
                <span>
                    Zobrazeno <?= count($orders) ?> z <?= $pagination['total_orders'] ?> objednávek
                    (stránka <?= $pagination['current_page'] ?> z <?= $pagination['total_pages'] ?>)
                </span>
                <div>
                    <?= renderPagination($pagination, $base_url, $current_params) ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/orders.js"></script>
</body>
</html>
