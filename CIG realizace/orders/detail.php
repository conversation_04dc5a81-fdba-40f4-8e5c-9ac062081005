<?php
// Include authentication check
require_once '../includes/auth_check.php';
require_once '../includes/order_functions.php';

// Get current user
$current_user = getCurrentUser();
$user_role = $current_user['role'];

// Get order ID
$order_id = intval($_GET['id'] ?? 0);

if (!$order_id) {
    header('Location: index.php');
    exit;
}

try {
    $pdo = getDbConnection();
    
    // Get order details
    $stmt = $pdo->prepare("
        SELECT o.*, 
               COUNT(oi.id) as items_count
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        WHERE o.id = ?
        GROUP BY o.id
    ");
    $stmt->execute([$order_id]);
    $order = $stmt->fetch();
    
    if (!$order) {
        header('Location: index.php?error=order_not_found');
        exit;
    }
    
    // Get order items
    $stmt = $pdo->prepare("
        SELECT * FROM order_items
        WHERE order_id = ?
        ORDER BY catalog_code
    ");
    $stmt->execute([$order_id]);
    $order_items = $stmt->fetchAll();

    // Get available technologies
    $stmt = $pdo->query("SELECT name FROM technologies WHERE is_active = 1 ORDER BY name");
    $technologies = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Get order history
    $stmt = $pdo->prepare("
        SELECT oh.*, u.username, u.full_name
        FROM order_history oh
        LEFT JOIN users u ON oh.user_id = u.id
        WHERE oh.order_id = ?
        ORDER BY oh.created_at DESC
        LIMIT 10
    ");
    $stmt->execute([$order_id]);
    $order_history = $stmt->fetchAll();

} catch (PDOException $e) {
    $error = "Chyba při načítání objednávky: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detail objednávky <?= htmlspecialchars($order['order_code']) ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/orders.css" rel="stylesheet">
    <link href="../assets/css/order_detail.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard.php">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($current_user['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user-edit me-2"></i>Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="orders-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="index.php">Objednávky</a></li>
                            <li class="breadcrumb-item active"><?= htmlspecialchars($order['order_code']) ?></li>
                        </ol>
                    </nav>
                    <h1><i class="fas fa-file-alt me-2"></i>Detail objednávky</h1>
                    <p class="mb-0">Kompletní informace o objednávce <?= htmlspecialchars($order['order_code']) ?></p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="index.php" class="btn btn-outline-light">
                            <i class="fas fa-arrow-left me-1"></i>Zpět na seznam
                        </a>
                        <?php if ($user_role === 'admin' || $user_role === 'grafik'): ?>
                        <button type="button" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#editStatusModal">
                            <i class="fas fa-edit me-1"></i>Upravit stav
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <!-- Order Information -->
        <div class="row">
            <div class="col-md-8">
                <!-- Order Header -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informace o objednávce</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Kód objednávky:</strong></td>
                                        <td><?= htmlspecialchars($order['order_code']) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Obchodník:</strong></td>
                                        <td>
                                            <span class="sales-rep-prefix"><?= htmlspecialchars($order['sales_rep']) ?></span>
                                            <?= htmlspecialchars($order['sales_rep_name'] ?: 'Neznámý') ?>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Datum objednávky:</strong></td>
                                        <td><?= date('d.m.Y', strtotime($order['order_date'])) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Počet položek:</strong></td>
                                        <td><span class="items-count"><?= $order['items_count'] ?></span></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Stav objednávky:</strong></td>
                                        <td>
                                            <?php $order_status = formatOrderStatus($order['status']); ?>
                                            <span class="status-badge <?= $order_status['class'] ?>">
                                                <?= $order_status['label'] ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Stav náhledu:</strong></td>
                                        <td>
                                            <?php $preview_status = formatPreviewStatus($order['preview_status']); ?>
                                            <span class="status-badge <?= $preview_status['class'] ?>">
                                                <?= $preview_status['label'] ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Vytvořeno:</strong></td>
                                        <td><?= date('d.m.Y H:i', strtotime($order['created_at'])) ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Aktualizováno:</strong></td>
                                        <td><?= date('d.m.Y H:i', strtotime($order['updated_at'])) ?></td>
                                    </tr>
                                    <?php if ($order['preview_approved_date']): ?>
                                    <tr>
                                        <td><strong>Náhled schválen:</strong></td>
                                        <td><?= date('d.m.Y', strtotime($order['preview_approved_date'])) ?></td>
                                    </tr>
                                    <?php endif; ?>
                                    <?php if ($order['expected_delivery_date']): ?>
                                    <tr>
                                        <td><strong>Očekávané dodání:</strong></td>
                                        <td><?= date('d.m.Y', strtotime($order['expected_delivery_date'])) ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>
                        
                        <?php if ($order['notes']): ?>
                        <div class="mt-3">
                            <strong>Poznámky:</strong>
                            <p class="mt-2"><?= nl2br(htmlspecialchars($order['notes'])) ?></p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Status Management Form -->
                <?php if ($user_role === 'admin' || $user_role === 'grafik'): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-edit me-2"></i>Správa stavu objednávky</h5>
                    </div>
                    <div class="card-body">
                        <form id="preview-status-form" class="status-form">
                            <input type="hidden" name="order_id" value="<?= $order['id'] ?>">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="preview_status" class="form-label">Stav náhledu</label>
                                    <select name="preview_status" id="preview_status" class="form-select" required>
                                        <option value="not_created" <?= $order['preview_status'] === 'not_created' ? 'selected' : '' ?>>Není vytvořen</option>
                                        <option value="sent_to_client" <?= $order['preview_status'] === 'sent_to_client' ? 'selected' : '' ?>>Odesláno na klienta</option>
                                        <option value="approved" <?= $order['preview_status'] === 'approved' ? 'selected' : '' ?>>Schváleno</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="notes" class="form-label">Poznámka ke změně (volitelné)</label>
                                    <input type="text" name="notes" id="notes" class="form-control" placeholder="Důvod změny stavu...">
                                </div>
                                <div class="col-md-2 d-flex align-items-end">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-save me-1"></i>Uložit změny
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Order Items -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Položky objednávky</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($order_items)): ?>
                        <p class="text-muted text-center">Žádné položky objednávky</p>
                        <?php else: ?>
                        <div class="table-responsive items-table">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Katalog</th>
                                        <th>Množství</th>
                                        <th>Stav zásob</th>
                                        <th>Datum objednání</th>
                                        <th>Datum naskladnění</th>
                                        <th>Technologie</th>
                                        <th>Relevantní</th>
                                        <?php if ($user_role === 'admin' || $user_role === 'obchodnik'): ?>
                                        <th>Akce</th>
                                        <?php endif; ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($order_items as $item): ?>
                                    <tr class="<?= $item['is_relevant'] ? '' : 'text-muted' ?>" data-item-id="<?= $item['id'] ?>">
                                        <td><?= htmlspecialchars($item['catalog_code'] ?: '-') ?></td>
                                        <td><?= number_format($item['quantity'], 4) ?></td>
                                        <td>
                                            <?php
                                            $inventory_status = [
                                                'not_in_stock' => ['label' => 'Není skladem', 'class' => 'inventory-not-in-stock'],
                                                'ordered' => ['label' => 'Objednáno', 'class' => 'inventory-ordered'],
                                                'in_stock' => ['label' => 'Skladem', 'class' => 'inventory-in-stock']
                                            ];
                                            $status = $inventory_status[$item['inventory_status']] ?? ['label' => 'Neznámý', 'class' => 'bg-secondary'];
                                            ?>
                                            <?php if ($user_role === 'admin' || $user_role === 'realizator'): ?>
                                            <select class="form-select form-select-sm inventory-select" data-item-id="<?= $item['id'] ?>">
                                                <option value="not_in_stock" <?= $item['inventory_status'] === 'not_in_stock' ? 'selected' : '' ?>>Není skladem</option>
                                                <option value="ordered" <?= $item['inventory_status'] === 'ordered' ? 'selected' : '' ?>>Objednáno</option>
                                                <option value="in_stock" <?= $item['inventory_status'] === 'in_stock' ? 'selected' : '' ?>>Skladem</option>
                                            </select>
                                            <?php else: ?>
                                            <span class="badge <?= $status['class'] ?> inventory-badge"><?= $status['label'] ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= $item['goods_ordered_date'] ? date('d.m.Y', strtotime($item['goods_ordered_date'])) : '-' ?></td>
                                        <td><?= $item['goods_stocked_date'] ? date('d.m.Y', strtotime($item['goods_stocked_date'])) : '-' ?></td>
                                        <td>
                                            <?php if ($user_role === 'admin' || $user_role === 'obchodnik' || $user_role === 'grafik'): ?>
                                            <div class="d-flex gap-1">
                                                <select class="form-select form-select-sm technology-select" data-item-id="<?= $item['id'] ?>" style="max-width: 120px;">
                                                    <option value="">Vyberte...</option>
                                                    <?php foreach ($technologies as $tech): ?>
                                                    <option value="<?= htmlspecialchars($tech) ?>" <?= $item['technology_assignment'] === $tech ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($tech) ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <input type="text" class="form-control form-control-sm technology-input"
                                                       data-item-id="<?= $item['id'] ?>"
                                                       value="<?= htmlspecialchars($item['technology_assignment'] ?: '') ?>"
                                                       placeholder="Vlastní..." style="max-width: 100px;">
                                                <button type="button" class="btn btn-sm btn-success technology-save-btn" data-item-id="<?= $item['id'] ?>">
                                                    <i class="fas fa-save"></i>
                                                </button>
                                            </div>
                                            <?php else: ?>
                                            <span class="technology-display"><?= htmlspecialchars($item['technology_assignment'] ?: '-') ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user_role === 'admin' || $user_role === 'obchodnik'): ?>
                                            <i class="relevance-toggle <?= $item['is_relevant'] ? 'relevant' : 'irrelevant' ?>"
                                               data-item-id="<?= $item['id'] ?>"
                                               title="Klikněte pro změnu relevance">
                                                <?= $item['is_relevant'] ? '<i class="fas fa-check"></i>' : '<i class="fas fa-times"></i>' ?>
                                            </i>
                                            <?php else: ?>
                                            <?php if ($item['is_relevant']): ?>
                                            <i class="fas fa-check text-success"></i>
                                            <?php else: ?>
                                            <i class="fas fa-times text-danger"></i>
                                            <?php endif; ?>
                                            <?php endif; ?>
                                        </td>
                                        <?php if ($user_role === 'admin' || $user_role === 'obchodnik'): ?>
                                        <td>
                                            <div class="item-actions">
                                                <button type="button" class="item-action-btn btn-edit" title="Upravit položku">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                        </td>
                                        <?php endif; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Quick Actions -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-bolt me-2"></i>Rychlé akce</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <?php if ($user_role === 'admin' || $user_role === 'grafik'): ?>
                            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#editStatusModal">
                                <i class="fas fa-edit me-1"></i>Změnit stav náhledu
                            </button>
                            <?php endif; ?>

                            <?php if (($user_role === 'admin' || $user_role === 'realizator') && !$order['is_completed'] && $order['preview_status'] === 'approved'): ?>
                            <button type="button" class="btn btn-success" onclick="completeOrder(<?= $order['id'] ?>)">
                                <i class="fas fa-check-circle me-1"></i>Označit jako dokončené
                            </button>
                            <?php elseif ($order['is_completed']): ?>
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle me-1"></i>Objednávka je dokončena
                                <?php if ($order['completed_date']): ?>
                                <br><small>Dokončeno: <?= date('d.m.Y H:i', strtotime($order['completed_date'])) ?></small>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>

                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-list me-1"></i>Zpět na seznam
                            </a>
                            
                            <a href="pending.php" class="btn btn-outline-warning">
                                <i class="fas fa-clock me-1"></i>Čekající objednávky
                            </a>

                            <a href="history.php?id=<?= $order['id'] ?>" class="btn btn-outline-info">
                                <i class="fas fa-history me-1"></i>Kompletní historie
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Order History -->
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-history me-2"></i>Historie změn</h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($order_history)): ?>
                        <p class="text-muted text-center">Žádné změny v historii</p>
                        <?php else: ?>
                        <div class="history-timeline">
                            <?php foreach ($order_history as $history): ?>
                            <div class="history-item">
                                <div class="history-meta">
                                    <i class="fas fa-clock me-1"></i>
                                    <?= date('d.m.Y H:i', strtotime($history['created_at'])) ?>
                                    - <?= htmlspecialchars($history['full_name'] ?: $history['username']) ?>
                                </div>
                                <div class="history-description">
                                    <?= htmlspecialchars($history['description']) ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Change Modal (placeholder) -->
    <div class="modal fade" id="editStatusModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Změna stavu objednávky</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-muted">Funkce změny stavu bude implementována v dalším kroku.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Zavřít</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/orders.js"></script>
    <script src="../assets/js/order_detail.js"></script>
</body>
</html>
