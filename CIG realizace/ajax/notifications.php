<?php
/**
 * AJAX endpoint for notification management
 * CIG Realizace - Phase 06
 */

// Set JSON response header
header('Content-Type: application/json');

// Include authentication check and notification manager
require_once '../includes/auth_check.php';
require_once '../classes/NotificationManager.php';

// Check if user is logged in
$current_user = getCurrentUser();
if (!$current_user) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Nejste přihlášeni']);
    exit;
}

// Get action from request
$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_notifications':
            $limit = intval($_GET['limit'] ?? 20);
            $unread_only = isset($_GET['unread_only']) && $_GET['unread_only'] === 'true';
            
            $notifications = NotificationManager::getUserNotifications($current_user['id'], $limit, $unread_only);
            $formatted_notifications = [];
            
            foreach ($notifications as $notification) {
                $formatted_notifications[] = NotificationManager::formatNotification($notification);
            }
            
            echo json_encode([
                'success' => true,
                'notifications' => $formatted_notifications,
                'count' => count($formatted_notifications)
            ]);
            break;
            
        case 'get_unread_count':
            $count = NotificationManager::getUnreadCount($current_user['id']);
            
            echo json_encode([
                'success' => true,
                'unread_count' => $count
            ]);
            break;
            
        case 'mark_as_read':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Metoda není povolena');
            }
            
            $notification_id = intval($_POST['notification_id'] ?? 0);
            if (!$notification_id) {
                throw new Exception('Neplatné ID notifikace');
            }
            
            $success = NotificationManager::markAsRead($notification_id, $current_user['id']);
            
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'Notifikace označena jako přečtená' : 'Chyba při označování notifikace'
            ]);
            break;
            
        case 'mark_all_as_read':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Metoda není povolena');
            }
            
            $count = NotificationManager::markAllAsRead($current_user['id']);
            
            echo json_encode([
                'success' => true,
                'message' => "Označeno {$count} notifikací jako přečtené",
                'marked_count' => $count
            ]);
            break;
            
        case 'delete_notification':
            if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
                throw new Exception('Metoda není povolena');
            }
            
            $notification_id = intval($_POST['notification_id'] ?? 0);
            if (!$notification_id) {
                throw new Exception('Neplatné ID notifikace');
            }
            
            $success = NotificationManager::delete($notification_id, $current_user['id']);
            
            echo json_encode([
                'success' => $success,
                'message' => $success ? 'Notifikace byla smazána' : 'Chyba při mazání notifikace'
            ]);
            break;
            
        case 'get_statistics':
            $stats = NotificationManager::getStatistics($current_user['id']);
            
            echo json_encode([
                'success' => true,
                'statistics' => $stats
            ]);
            break;
            
        default:
            throw new Exception('Neplatná akce');
    }
    
} catch (Exception $e) {
    error_log("Notifications AJAX error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
