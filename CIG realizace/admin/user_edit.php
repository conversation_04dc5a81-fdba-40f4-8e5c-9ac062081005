<?php
// Include authentication check
require_once '../includes/auth_check.php';

// Require admin role
requireRole('admin');

// Include User class
require_once '../classes/User.php';

// Initialize User class
$userManager = new User($pdo);

// Get user ID from URL
$user_id = $_GET['id'] ?? '';
if (!$user_id || !is_numeric($user_id)) {
    setFlashMessage('error', 'Neplatné ID uživatele.');
    header('Location: users.php');
    exit;
}

// Get user data
try {
    $user = $userManager->getUserById($user_id);
    if (!$user) {
        setFlashMessage('error', 'Uživatel nebyl nalezen.');
        header('Location: users.php');
        exit;
    }
} catch (Exception $e) {
    setFlashMessage('error', 'Chyba při načítání uživatele: ' . $e->getMessage());
    header('Location: users.php');
    exit;
}

// Handle form submission
$message = '';
$messageType = '';
$formData = [
    'username' => $user['username'],
    'full_name' => $user['full_name'],
    'role' => $user['role'],
    'email' => $user['email']
];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Neplatný CSRF token.';
        $messageType = 'error';
    } else {
        $formData = [
            'username' => trim($_POST['username'] ?? ''),
            'full_name' => trim($_POST['full_name'] ?? ''),
            'role' => $_POST['role'] ?? '',
            'email' => trim($_POST['email'] ?? '') ?: null,
            'password' => $_POST['password'] ?? '',
            'password_confirm' => $_POST['password_confirm'] ?? ''
        ];
        
        // Validation
        $errors = [];
        
        if (empty($formData['username'])) {
            $errors[] = 'Uživatelské jméno je povinné.';
        } elseif (strlen($formData['username']) < 3) {
            $errors[] = 'Uživatelské jméno musí mít alespoň 3 znaky.';
        }
        
        if (empty($formData['full_name'])) {
            $errors[] = 'Celé jméno je povinné.';
        }
        
        if (empty($formData['role'])) {
            $errors[] = 'Role je povinná.';
        }
        
        if ($formData['email'] && !filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Neplatný formát emailu.';
        }
        
        // Password validation (only if provided)
        if (!empty($formData['password'])) {
            if (strlen($formData['password']) < 6) {
                $errors[] = 'Heslo musí mít alespoň 6 znaků.';
            }
            
            if ($formData['password'] !== $formData['password_confirm']) {
                $errors[] = 'Hesla se neshodují.';
            }
        }
        
        if (empty($errors)) {
            try {
                $userManager->updateUser(
                    $user_id,
                    $formData['username'],
                    $formData['full_name'],
                    $formData['role'],
                    $formData['email'],
                    !empty($formData['password']) ? $formData['password'] : null
                );
                
                setFlashMessage('success', 'Uživatel byl úspěšně aktualizován.');
                header('Location: users.php');
                exit;
                
            } catch (Exception $e) {
                $message = $e->getMessage();
                $messageType = 'error';
            }
        } else {
            $message = implode('<br>', $errors);
            $messageType = 'error';
        }
    }
}

// Get available roles
$roles = $userManager->getAvailableRoles();
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Upravit uživatele</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="users.php">
                            <i class="fas fa-users me-1"></i>Uživatelé
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= e($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="users.php">Uživatelé</a></li>
                        <li class="breadcrumb-item active">Upravit uživatele</li>
                    </ol>
                </nav>

                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-user-edit me-2"></i>Upravit uživatele</h1>
                    <a href="users.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Zpět na seznam
                    </a>
                </div>

                <!-- Flash Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType === 'error' ? 'danger' : 'success' ?> alert-dismissible fade show">
                    <i class="fas fa-<?= $messageType === 'error' ? 'exclamation-circle' : 'check-circle' ?> me-2"></i>
                    <?= $message ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Edit User Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user me-2"></i>
                            Úprava uživatele: <?= e($user['full_name']) ?>
                            <?php if ($user['username'] === 'admin'): ?>
                            <span class="badge bg-warning text-dark ms-2">Administrátor</span>
                            <?php endif; ?>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <input type="hidden" name="csrf_token" value="<?= getCSRFToken() ?>">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>Uživatelské jméno *
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           value="<?= e($formData['username']) ?>" required
                                           <?= $user['username'] === 'admin' ? 'readonly' : '' ?>>
                                    <?php if ($user['username'] === 'admin'): ?>
                                    <div class="form-text text-warning">Uživatelské jméno administrátora nelze změnit</div>
                                    <?php else: ?>
                                    <div class="form-text">Minimálně 3 znaky, pouze písmena, čísla a podtržítka</div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>Celé jméno *
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?= e($formData['full_name']) ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>Role *
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        <?php foreach ($roles as $role_key => $role_name): ?>
                                        <option value="<?= e($role_key) ?>" <?= $formData['role'] === $role_key ? 'selected' : '' ?>>
                                            <?= e($role_name) ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?= e($formData['email'] ?? '') ?>">
                                    <div class="form-text">Volitelné</div>
                                </div>
                            </div>
                            
                            <hr>
                            
                            <h6><i class="fas fa-key me-1"></i>Změna hesla</h6>
                            <p class="text-muted small">Vyplňte pouze pokud chcete změnit heslo</p>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Nové heslo
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password">
                                    <div class="form-text">Minimálně 6 znaků</div>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="password_confirm" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Potvrzení hesla
                                    </label>
                                    <input type="password" class="form-control" id="password_confirm" name="password_confirm">
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <a href="users.php" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-2"></i>Zrušit
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>Uložit změny
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- User Info -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6><i class="fas fa-info-circle me-1"></i>Informace o uživateli</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Vytvořen:</strong> <?= formatDateTime($user['created_at']) ?></p>
                                <p><strong>Stav:</strong> 
                                    <span class="badge bg-<?= $user['is_active'] ? 'success' : 'secondary' ?>">
                                        <?= $user['is_active'] ? 'Aktivní' : 'Neaktivní' ?>
                                    </span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Poslední aktualizace:</strong> <?= $user['updated_at'] ? formatDateTime($user['updated_at']) : 'Nikdy' ?></p>
                                <p><strong>Poslední přihlášení:</strong> <?= $user['last_login'] ? formatDateTime($user['last_login']) : 'Nikdy' ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('password_confirm').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirm = this.value;
            
            if (confirm && password !== confirm) {
                this.setCustomValidity('Hesla se neshodují');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Clear password confirmation when password is cleared
        document.getElementById('password').addEventListener('input', function() {
            const confirm = document.getElementById('password_confirm');
            if (!this.value) {
                confirm.value = '';
                confirm.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
