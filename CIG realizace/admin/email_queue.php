<?php
/**
 * Admin Email Queue Management
 * CIG Realizace - Phase 08
 */

require_once '../includes/auth_check.php';
require_once '../includes/email_functions.php';

// Check admin access
if (!hasRole('admin')) {
    header('Location: ../includes/access_denied.php');
    exit;
}

$current_user = getCurrentUser();
$page_title = 'Email fronta';

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;
$offset = ($page - 1) * $limit;

// Filters
$status_filter = $_GET['status'] ?? '';
$template_filter = $_GET['template'] ?? '';

// Handle actions
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'retry_failed':
            $emailId = intval($_POST['email_id'] ?? 0);
            if ($emailId > 0) {
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("
                        UPDATE notification_queue 
                        SET status = 'pending', attempts = 0, error_message = NULL,
                            scheduled_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ? AND status = 'failed'
                    ");
                    
                    if ($stmt->execute([$emailId])) {
                        $message = 'Email byl znovu zařazen do fronty.';
                    } else {
                        $error = 'Nepodařilo se znovu zařadit email do fronty.';
                    }
                } catch (Exception $e) {
                    $error = 'Chyba při zařazování emailu do fronty.';
                }
            }
            break;
            
        case 'delete_email':
            $emailId = intval($_POST['email_id'] ?? 0);
            if ($emailId > 0) {
                try {
                    $pdo = getDbConnection();
                    $stmt = $pdo->prepare("DELETE FROM notification_queue WHERE id = ?");
                    
                    if ($stmt->execute([$emailId])) {
                        $message = 'Email byl smazán z fronty.';
                    } else {
                        $error = 'Nepodařilo se smazat email z fronty.';
                    }
                } catch (Exception $e) {
                    $error = 'Chyba při mazání emailu z fronty.';
                }
            }
            break;
    }
}

// Get queue data
try {
    $pdo = getDbConnection();
    
    // Build WHERE clause
    $whereConditions = [];
    $params = [];
    
    if (!empty($status_filter)) {
        $whereConditions[] = "status = ?";
        $params[] = $status_filter;
    }
    
    if (!empty($template_filter)) {
        $whereConditions[] = "template = ?";
        $params[] = $template_filter;
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // Get total count
    $countSql = "SELECT COUNT(*) as total FROM notification_queue $whereClause";
    $stmt = $pdo->prepare($countSql);
    $stmt->execute($params);
    $totalEmails = $stmt->fetch()['total'];
    
    // Get emails for current page
    $sql = "
        SELECT * FROM notification_queue 
        $whereClause
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $emails = $stmt->fetchAll();
    
    // Get available templates
    $stmt = $pdo->prepare("SELECT DISTINCT template FROM notification_queue ORDER BY template");
    $stmt->execute();
    $templates = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (Exception $e) {
    $error = 'Chyba při načítání dat z fronty.';
    $emails = [];
    $totalEmails = 0;
    $templates = [];
}

$totalPages = ceil($totalEmails / $limit);
?>

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    <style>
        .status-badge {
            font-size: 0.8em;
        }
        .email-preview {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .template-badge {
            font-size: 0.75em;
        }
        .priority-indicator {
            width: 4px;
            height: 100%;
            position: absolute;
            left: 0;
            top: 0;
        }
        .priority-1, .priority-2 { background: #dc3545; }
        .priority-3, .priority-4 { background: #fd7e14; }
        .priority-5, .priority-6 { background: #ffc107; }
        .priority-7, .priority-8 { background: #28a745; }
        .priority-9, .priority-10 { background: #6c757d; }
        
        .table-row {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="../dashboard.php">
                                <i class="fas fa-home"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.php">
                                <i class="fas fa-users"></i> Uživatelé
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="notifications.php">
                                <i class="fas fa-bell"></i> Notifikace
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="email_queue.php">
                                <i class="fas fa-envelope"></i> Email fronta
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="system_activity.php">
                                <i class="fas fa-chart-line"></i> Systémová aktivita
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="fas fa-envelope text-primary"></i>
                        <?= htmlspecialchars($page_title) ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="notifications.php" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left"></i> Zpět na notifikace
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Filters -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="status" class="form-label">Stav</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="">Všechny stavy</option>
                                    <option value="pending" <?= $status_filter === 'pending' ? 'selected' : '' ?>>Čekající</option>
                                    <option value="processing" <?= $status_filter === 'processing' ? 'selected' : '' ?>>Zpracovává se</option>
                                    <option value="sent" <?= $status_filter === 'sent' ? 'selected' : '' ?>>Odesláno</option>
                                    <option value="failed" <?= $status_filter === 'failed' ? 'selected' : '' ?>>Neúspěšné</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="template" class="form-label">Template</label>
                                <select name="template" id="template" class="form-select">
                                    <option value="">Všechny templates</option>
                                    <?php foreach ($templates as $template): ?>
                                    <option value="<?= htmlspecialchars($template) ?>" 
                                            <?= $template_filter === $template ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($template) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-filter"></i> Filtrovat
                                    </button>
                                    <a href="email_queue.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Zrušit
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Email Queue Table -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 
                            Email fronta (<?= number_format($totalEmails) ?> emailů)
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($emails)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Žádné emaily ve frontě</p>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Příjemce</th>
                                        <th>Předmět</th>
                                        <th>Template</th>
                                        <th>Stav</th>
                                        <th>Priorita</th>
                                        <th>Pokusy</th>
                                        <th>Vytvořeno</th>
                                        <th>Akce</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($emails as $email): ?>
                                    <tr class="table-row">
                                        <div class="priority-indicator priority-<?= $email['priority'] ?>"></div>
                                        <td>
                                            <div class="email-preview">
                                                <strong><?= htmlspecialchars($email['recipient_name'] ?? 'N/A') ?></strong><br>
                                                <small class="text-muted"><?= htmlspecialchars($email['recipient_email']) ?></small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="email-preview" title="<?= htmlspecialchars($email['subject']) ?>">
                                                <?= htmlspecialchars($email['subject']) ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info template-badge">
                                                <?= htmlspecialchars($email['template']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $statusColors = [
                                                'pending' => 'warning',
                                                'processing' => 'info',
                                                'sent' => 'success',
                                                'failed' => 'danger'
                                            ];
                                            $statusTexts = [
                                                'pending' => 'Čekající',
                                                'processing' => 'Zpracovává se',
                                                'sent' => 'Odesláno',
                                                'failed' => 'Neúspěšné'
                                            ];
                                            $statusColor = $statusColors[$email['status']] ?? 'secondary';
                                            $statusText = $statusTexts[$email['status']] ?? $email['status'];
                                            ?>
                                            <span class="badge bg-<?= $statusColor ?> status-badge">
                                                <?= $statusText ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <?= $email['priority'] ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= $email['attempts'] ?>/<?= $email['max_attempts'] ?>
                                            <?php if (!empty($email['error_message'])): ?>
                                            <i class="fas fa-exclamation-triangle text-danger" 
                                               title="<?= htmlspecialchars($email['error_message']) ?>"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <small>
                                                <?= date('d.m.Y H:i', strtotime($email['created_at'])) ?>
                                                <?php if ($email['sent_at']): ?>
                                                <br><span class="text-success">Odesláno: <?= date('d.m.Y H:i', strtotime($email['sent_at'])) ?></span>
                                                <?php endif; ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <?php if ($email['status'] === 'failed'): ?>
                                                <form method="post" class="d-inline">
                                                    <input type="hidden" name="action" value="retry_failed">
                                                    <input type="hidden" name="email_id" value="<?= $email['id'] ?>">
                                                    <button type="submit" class="btn btn-outline-warning btn-sm" 
                                                            title="Znovu odeslat">
                                                        <i class="fas fa-redo"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                                
                                                <form method="post" class="d-inline" 
                                                      onsubmit="return confirm('Opravdu chcete smazat tento email?')">
                                                    <input type="hidden" name="action" value="delete_email">
                                                    <input type="hidden" name="email_id" value="<?= $email['id'] ?>">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm" 
                                                            title="Smazat">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                        <div class="card-footer">
                            <nav aria-label="Email queue pagination">
                                <ul class="pagination justify-content-center mb-0">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $page - 1 ?>&status=<?= urlencode($status_filter) ?>&template=<?= urlencode($template_filter) ?>">
                                            Předchozí
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                    <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?page=<?= $i ?>&status=<?= urlencode($status_filter) ?>&template=<?= urlencode($template_filter) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?= $page + 1 ?>&status=<?= urlencode($status_filter) ?>&template=<?= urlencode($template_filter) ?>">
                                            Další
                                        </a>
                                    </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
