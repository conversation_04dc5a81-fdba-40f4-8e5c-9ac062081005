<?php
// Include authentication check
require_once '../includes/auth_check.php';

// Require admin role
requireRole('admin');

// Include User class
require_once '../classes/User.php';

// Initialize User class
$userManager = new User($pdo);

// Handle user actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Neplatný CSRF token.';
        $messageType = 'error';
    } else {
        $action = $_POST['action'] ?? '';
        $user_id = $_POST['user_id'] ?? '';
        
        try {
            switch ($action) {
                case 'toggle_status':
                    $userManager->toggleUserStatus($user_id);
                    $message = 'Stav uživatele byl změněn.';
                    $messageType = 'success';
                    break;
                    
                case 'delete':
                    $userManager->deleteUser($user_id);
                    $message = 'Uživatel byl smazán.';
                    $messageType = 'success';
                    break;
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
            $messageType = 'error';
        }
    }
}

// Get all users
try {
    $users = $userManager->getAllUsers(true); // Include inactive users
} catch (Exception $e) {
    $users = [];
    $message = 'Chyba při načítání uživatelů: ' . $e->getMessage();
    $messageType = 'error';
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Správa uživatelů</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="users.php">
                            <i class="fas fa-users me-1"></i>Uživatelé
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= e($_SESSION['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../dashboard.php"><i class="fas fa-home me-2"></i>Dashboard</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-users me-2"></i>Správa uživatelů</h1>
                    <a href="user_add.php" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Přidat uživatele
                    </a>
                </div>

                <!-- Flash Messages -->
                <?php if ($message): ?>
                <div class="alert alert-<?= $messageType === 'error' ? 'danger' : 'success' ?> alert-dismissible fade show">
                    <i class="fas fa-<?= $messageType === 'error' ? 'exclamation-circle' : 'check-circle' ?> me-2"></i>
                    <?= e($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- Users Table -->
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Žádní uživatelé</h5>
                            <p class="text-muted">Začněte přidáním prvního uživatele.</p>
                            <a href="user_add.php" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Přidat uživatele
                            </a>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Jméno</th>
                                        <th>Uživatelské jméno</th>
                                        <th>Role</th>
                                        <th>Email</th>
                                        <th>Stav</th>
                                        <th>Poslední přihlášení</th>
                                        <th>Akce</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                    <tr class="<?= $user['is_active'] ? '' : 'table-secondary' ?>">
                                        <td>
                                            <strong><?= e($user['full_name']) ?></strong>
                                            <?php if ($user['username'] === 'admin'): ?>
                                            <span class="badge bg-warning text-dark ms-2">Admin</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= e($user['username']) ?></td>
                                        <td>
                                            <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 
                                                                   ($user['role'] === 'obchodnik' ? 'primary' : 
                                                                    ($user['role'] === 'grafik' ? 'info' : 'success')) ?>">
                                                <?= e(getRoleDisplayName($user['role'])) ?>
                                            </span>
                                        </td>
                                        <td><?= e($user['email'] ?: '-') ?></td>
                                        <td>
                                            <?php if ($user['is_active']): ?>
                                            <span class="badge bg-success">Aktivní</span>
                                            <?php else: ?>
                                            <span class="badge bg-secondary">Neaktivní</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?= $user['last_login'] ? formatDateTime($user['last_login']) : 'Nikdy' ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="user_edit.php?id=<?= $user['id'] ?>" class="btn btn-outline-primary" title="Upravit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                
                                                <?php if ($user['username'] !== 'admin'): ?>
                                                <form method="POST" class="d-inline" onsubmit="return confirm('Opravdu chcete změnit stav tohoto uživatele?')">
                                                    <input type="hidden" name="csrf_token" value="<?= getCSRFToken() ?>">
                                                    <input type="hidden" name="action" value="toggle_status">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <button type="submit" class="btn btn-outline-<?= $user['is_active'] ? 'warning' : 'success' ?>" 
                                                            title="<?= $user['is_active'] ? 'Deaktivovat' : 'Aktivovat' ?>">
                                                        <i class="fas fa-<?= $user['is_active'] ? 'ban' : 'check' ?>"></i>
                                                    </button>
                                                </form>
                                                
                                                <form method="POST" class="d-inline" onsubmit="return confirm('Opravdu chcete smazat tohoto uživatele? Tato akce je nevratná.')">
                                                    <input type="hidden" name="csrf_token" value="<?= getCSRFToken() ?>">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="user_id" value="<?= $user['id'] ?>">
                                                    <button type="submit" class="btn btn-outline-danger" title="Smazat">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
