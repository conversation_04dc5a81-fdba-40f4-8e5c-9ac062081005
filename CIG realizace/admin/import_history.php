<?php
// Import History
// CIG Realizace - Order Management System

session_start();
require_once '../includes/auth_check.php';
require_once '../config/database.php';

// Check admin access
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$page_title = 'Historie importů';

// Get import history
try {
    $pdo = getDbConnection();
    
    // Pagination
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $per_page = 20;
    $offset = ($page - 1) * $per_page;
    
    // Get total count
    $count_stmt = $pdo->query("SELECT COUNT(*) as total FROM import_logs");
    $total_imports = $count_stmt->fetch()['total'];
    $total_pages = ceil($total_imports / $per_page);
    
    // Get imports with user info
    $stmt = $pdo->prepare("
        SELECT il.*, u.full_name as user_name
        FROM import_logs il
        LEFT JOIN users u ON il.user_id = u.id
        ORDER BY il.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$per_page, $offset]);
    $imports = $stmt->fetchAll();
    
} catch (Exception $e) {
    $error = 'Chyba při načítání historie importů: ' . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/dashboard.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i><?= htmlspecialchars($_SESSION['full_name']) ?>
                </span>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt"></i> Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs me-2"></i>Administrace</h5>
                    </div>
                    <div class="list-group list-group-flush">
                        <a href="../dashboard.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                        <a href="users.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-users me-2"></i>Správa uživatelů
                        </a>
                        <a href="import.php" class="list-group-item list-group-item-action">
                            <i class="fas fa-file-import me-2"></i>Import CSV
                        </a>
                        <a href="import_history.php" class="list-group-item list-group-item-action active">
                            <i class="fas fa-history me-2"></i>Historie importů
                        </a>
                    </div>
                </div>
            </div>

            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h4><i class="fas fa-history me-2"></i><?= $page_title ?></h4>
                        <a href="import.php" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>Nový import
                        </a>
                    </div>
                    <div class="card-body">
                        
                        <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <?= htmlspecialchars($error) ?>
                        </div>
                        <?php endif; ?>

                        <?php if (empty($imports)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Zatím nebyly provedeny žádné importy</h5>
                            <p class="text-muted">Začněte importem CSV souboru s objednávkami.</p>
                            <a href="import.php" class="btn btn-primary">
                                <i class="fas fa-file-import me-1"></i>Importovat CSV
                            </a>
                        </div>
                        <?php else: ?>

                        <!-- Summary Stats -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h5><?= $total_imports ?></h5>
                                        <small>Celkem importů</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <?php
                                        $success_count = 0;
                                        foreach ($imports as $import) {
                                            if ($import['status'] === 'success') $success_count++;
                                        }
                                        ?>
                                        <h5><?= $success_count ?></h5>
                                        <small>Úspěšné</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <?php
                                        $total_orders = 0;
                                        foreach ($imports as $import) {
                                            $total_orders += $import['orders_created'];
                                        }
                                        ?>
                                        <h5><?= $total_orders ?></h5>
                                        <small>Objednávky</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <?php
                                        $total_items = 0;
                                        foreach ($imports as $import) {
                                            $total_items += $import['items_created'];
                                        }
                                        ?>
                                        <h5><?= $total_items ?></h5>
                                        <small>Položky</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Import History Table -->
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Datum</th>
                                        <th>Soubor</th>
                                        <th>Uživatel</th>
                                        <th>Status</th>
                                        <th>Řádky</th>
                                        <th>Objednávky</th>
                                        <th>Položky</th>
                                        <th>Čas</th>
                                        <th>Akce</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($imports as $import): ?>
                                    <tr>
                                        <td>
                                            <small><?= date('d.m.Y H:i', strtotime($import['created_at'])) ?></small>
                                        </td>
                                        <td>
                                            <i class="fas fa-file-csv me-1"></i>
                                            <?= htmlspecialchars($import['filename']) ?>
                                        </td>
                                        <td><?= htmlspecialchars($import['user_name'] ?? 'Neznámý') ?></td>
                                        <td>
                                            <?php if ($import['status'] === 'success'): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>Úspěch
                                                </span>
                                            <?php elseif ($import['status'] === 'warning'): ?>
                                                <span class="badge bg-warning">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>Varování
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-times me-1"></i>Chyba
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= number_format($import['total_rows']) ?></td>
                                        <td><?= number_format($import['orders_created']) ?></td>
                                        <td><?= number_format($import['items_created']) ?></td>
                                        <td>
                                            <?php if ($import['processing_time']): ?>
                                                <?= $import['processing_time'] ?>s
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($import['errors_count'] > 0 || $import['warnings_count'] > 0): ?>
                                            <button class="btn btn-sm btn-outline-info" 
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#detailModal<?= $import['id'] ?>">
                                                <i class="fas fa-info-circle"></i>
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="Import history pagination">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page - 1 ?>">Předchozí</a>
                                </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                                </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?= $page + 1 ?>">Další</a>
                                </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                        <?php endif; ?>

                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detail Modals -->
    <?php foreach ($imports as $import): ?>
    <?php if ($import['errors_count'] > 0 || $import['warnings_count'] > 0): ?>
    <div class="modal fade" id="detailModal<?= $import['id'] ?>" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detail importu - <?= htmlspecialchars($import['filename']) ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Datum:</strong> <?= date('d.m.Y H:i:s', strtotime($import['created_at'])) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Uživatel:</strong> <?= htmlspecialchars($import['user_name'] ?? 'Neznámý') ?>
                        </div>
                    </div>
                    
                    <?php if ($import['errors_count'] > 0): ?>
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-circle me-2"></i>Chyby (<?= $import['errors_count'] ?>)</h6>
                        <?php if ($import['error_details']): ?>
                        <pre class="mb-0"><?= htmlspecialchars($import['error_details']) ?></pre>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($import['warnings_count'] > 0): ?>
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Varování (<?= $import['warnings_count'] ?>)</h6>
                        <p class="mb-0">Počet varování během importu.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
    <?php endforeach; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
