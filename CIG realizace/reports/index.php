<?php
require_once '../includes/session.php';
require_once '../includes/auth_check.php';
require_once '../config/database.php';
require_once '../classes/HistoryLogger.php';
require_once '../classes/NotificationManager.php';

// Check admin access
if ($current_user['role'] !== 'admin') {
    header('Location: ../includes/access_denied.php');
    exit;
}

$page_title = 'Reporty a statistiky';

try {
    $pdo = getDbConnection();
    
    // Get basic statistics
    $stats = [];
    
    // Total orders
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
    $stats['total_orders'] = $stmt->fetch()['total'] ?? 0;
    
    // Orders by status
    $stmt = $pdo->query("
        SELECT preview_status, COUNT(*) as count 
        FROM orders 
        GROUP BY preview_status
    ");
    $status_stats = $stmt->fetchAll();
    $stats['by_status'] = [];
    foreach ($status_stats as $stat) {
        $stats['by_status'][$stat['preview_status']] = $stat['count'];
    }
    
    // Orders by sales rep
    $stmt = $pdo->query("
        SELECT sales_rep_name, COUNT(*) as count 
        FROM orders 
        GROUP BY sales_rep_name
        ORDER BY count DESC
    ");
    $stats['by_sales_rep'] = $stmt->fetchAll();
    
    // Activity statistics (last 30 days)
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_actions,
            COUNT(DISTINCT order_id) as orders_affected,
            COUNT(DISTINCT user_id) as active_users
        FROM order_history 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    ");
    $stats['activity'] = $stmt->fetch();
    
    // User activity (last 30 days)
    $stmt = $pdo->query("
        SELECT u.username, u.full_name, u.role,
               COUNT(oh.id) as total_actions,
               MAX(oh.created_at) as last_activity
        FROM users u
        LEFT JOIN order_history oh ON u.id = oh.user_id 
            AND oh.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        WHERE u.is_active = 1
        GROUP BY u.id
        ORDER BY total_actions DESC
    ");
    $stats['user_activity'] = $stmt->fetchAll();
    
    // Recent imports
    $stmt = $pdo->query("
        SELECT filename, orders_created, items_created, created_at, status
        FROM import_history 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stats['recent_imports'] = $stmt->fetchAll();
    
} catch (PDOException $e) {
    $db_error = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/admin.css" rel="stylesheet">
    <link href="../assets/css/reports.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-chart-bar me-2"></i>CIG Realizace
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="../dashboard.php">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="../logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>Odhlásit
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1><i class="fas fa-chart-line me-2"></i><?= $page_title ?></h1>
                    <div class="btn-group">
                        <button type="button" class="btn btn-outline-primary" onclick="exportReport('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>Export PDF
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="exportReport('excel')">
                            <i class="fas fa-file-excel me-1"></i>Export Excel
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if (isset($db_error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Chyba připojení k databázi: <?= htmlspecialchars($db_error) ?>
        </div>
        <?php else: ?>

        <!-- Quick Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= number_format($stats['total_orders']) ?></h4>
                                <p class="mb-0">Celkem objednávek</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-shopping-cart fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= number_format($stats['activity']['total_actions'] ?? 0) ?></h4>
                                <p class="mb-0">Akce (30 dní)</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-history fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= number_format($stats['activity']['orders_affected'] ?? 0) ?></h4>
                                <p class="mb-0">Upravené objednávky</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-edit fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4><?= number_format($stats['activity']['active_users'] ?? 0) ?></h4>
                                <p class="mb-0">Aktivní uživatelé</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-pie me-2"></i>Objednávky podle stavu</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="statusChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-bar me-2"></i>Objednávky podle obchodníků</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesRepChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Report Links -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h5>Aktivita uživatelů</h5>
                        <p class="text-muted">Detailní přehled aktivit všech uživatelů</p>
                        <a href="../admin/user_activity.php" class="btn btn-primary">
                            <i class="fas fa-eye me-1"></i>Zobrazit
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-server fa-3x text-info mb-3"></i>
                        <h5>Systémová aktivita</h5>
                        <p class="text-muted">Přehled všech změn v systému</p>
                        <a href="../admin/system_activity.php" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>Zobrazit
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body text-center">
                        <i class="fas fa-file-import fa-3x text-success mb-3"></i>
                        <h5>Historie importů</h5>
                        <p class="text-muted">Přehled všech CSV importů</p>
                        <a href="../admin/import_history.php" class="btn btn-success">
                            <i class="fas fa-eye me-1"></i>Zobrazit
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/js/reports.js"></script>
    <script>
        // Initialize charts with data
        const statusData = <?= json_encode($stats['by_status'] ?? []) ?>;
        const salesRepData = <?= json_encode($stats['by_sales_rep'] ?? []) ?>;
        
        // Initialize reports page
        if (typeof Reports !== 'undefined') {
            Reports.init();
            Reports.initStatusChart(statusData);
            Reports.initSalesRepChart(salesRepData);
        }
    </script>
</body>
</html>
