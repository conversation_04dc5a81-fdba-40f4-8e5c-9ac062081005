<?php
// Include authentication check
require_once '../includes/auth_check.php';
require_once '../includes/order_functions.php';

// Get current user
$current_user = getCurrentUser();
$user_role = $current_user['role'];

// Get current month and year from URL parameters
$current_month = intval($_GET['month'] ?? date('n'));
$current_year = intval($_GET['year'] ?? date('Y'));

// Validate month and year
if ($current_month < 1 || $current_month > 12) {
    $current_month = date('n');
}
if ($current_year < 2020 || $current_year > 2030) {
    $current_year = date('Y');
}

// Get view type (month, week, day)
$view_type = $_GET['view'] ?? 'month';
if (!in_array($view_type, ['month', 'week', 'day'])) {
    $view_type = 'month';
}

// Get filters
$filters = [
    'sales_rep' => $_GET['sales_rep'] ?? '',
    'technology' => $_GET['technology'] ?? '',
    'search' => $_GET['search'] ?? ''
];

try {
    $pdo = getDbConnection();
    
    // Get available sales representatives for filter
    $sales_reps = getSalesRepresentatives();
    
    // Get available technologies for filter
    $stmt = $pdo->query("SELECT DISTINCT name FROM technologies WHERE is_active = 1 ORDER BY name");
    $technologies = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch (PDOException $e) {
    $error = "Chyba při načítání dat: " . $e->getMessage();
}

// Calculate calendar dates
$first_day_of_month = mktime(0, 0, 0, $current_month, 1, $current_year);
$last_day_of_month = mktime(0, 0, 0, $current_month + 1, 0, $current_year);
$days_in_month = date('t', $first_day_of_month);
$first_day_weekday = date('N', $first_day_of_month); // 1 = Monday, 7 = Sunday

// Previous and next month links
$prev_month = $current_month - 1;
$prev_year = $current_year;
if ($prev_month < 1) {
    $prev_month = 12;
    $prev_year--;
}

$next_month = $current_month + 1;
$next_year = $current_year;
if ($next_month > 12) {
    $next_month = 1;
    $next_year++;
}

// Month names in Czech
$month_names = [
    1 => 'Leden', 2 => 'Únor', 3 => 'Březen', 4 => 'Duben',
    5 => 'Květen', 6 => 'Červen', 7 => 'Červenec', 8 => 'Srpen',
    9 => 'Září', 10 => 'Říjen', 11 => 'Listopad', 12 => 'Prosinec'
];

// Day names in Czech
$day_names = ['Po', 'Út', 'St', 'Čt', 'Pá', 'So', 'Ne'];
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalendář objednávek - CIG Realizace</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/orders.css" rel="stylesheet">
    <link href="../assets/css/calendar.css" rel="stylesheet">
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="../dashboard.php">
                <i class="fas fa-clipboard-list me-2"></i>CIG Realizace
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../dashboard.php">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../orders/index.php">
                            <i class="fas fa-list me-1"></i>Objednávky
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i><?= htmlspecialchars($current_user['full_name']) ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="../profile.php"><i class="fas fa-user-edit me-2"></i>Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php"><i class="fas fa-sign-out-alt me-2"></i>Odhlásit se</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Header -->
    <section class="orders-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="../dashboard.php">Dashboard</a></li>
                            <li class="breadcrumb-item active">Kalendář</li>
                        </ol>
                    </nav>
                    <h1><i class="fas fa-calendar-alt me-2"></i>Kalendář objednávek</h1>
                    <p class="mb-0">Plánování a přehled schválených objednávek</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex gap-2 justify-content-end">
                        <a href="../orders/pending.php" class="btn btn-outline-light">
                            <i class="fas fa-clock me-1"></i>Čekající objednávky
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container">
        <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i><?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <!-- Calendar Header -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <!-- Navigation -->
                    <div class="col-md-4">
                        <div class="calendar-navigation d-flex align-items-center gap-3">
                            <a href="?month=<?= $prev_month ?>&year=<?= $prev_year ?>&view=<?= $view_type ?>" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <h3 class="mb-0" id="current-month"><?= $month_names[$current_month] ?> <?= $current_year ?></h3>
                            <a href="?month=<?= $next_month ?>&year=<?= $next_year ?>&view=<?= $view_type ?>" 
                               class="btn btn-outline-primary">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </div>
                    </div>
                    
                    <!-- View Type -->
                    <div class="col-md-4 text-center">
                        <div class="btn-group" role="group">
                            <a href="?month=<?= $current_month ?>&year=<?= $current_year ?>&view=month" 
                               class="btn <?= $view_type === 'month' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                Měsíc
                            </a>
                            <a href="?month=<?= $current_month ?>&year=<?= $current_year ?>&view=week" 
                               class="btn <?= $view_type === 'week' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                Týden
                            </a>
                            <a href="?month=<?= $current_month ?>&year=<?= $current_year ?>&view=day" 
                               class="btn <?= $view_type === 'day' ? 'btn-primary' : 'btn-outline-primary' ?>">
                                Den
                            </a>
                        </div>
                    </div>
                    
                    <!-- Today Button -->
                    <div class="col-md-4 text-end">
                        <a href="?month=<?= date('n') ?>&year=<?= date('Y') ?>&view=<?= $view_type ?>" 
                           class="btn btn-success">
                            <i class="fas fa-calendar-day me-1"></i>Dnes
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <input type="hidden" name="month" value="<?= $current_month ?>">
                    <input type="hidden" name="year" value="<?= $current_year ?>">
                    <input type="hidden" name="view" value="<?= $view_type ?>">
                    
                    <div class="col-md-3">
                        <label for="sales_rep" class="form-label">Obchodník</label>
                        <select name="sales_rep" id="sales_rep" class="form-select">
                            <option value="">Všichni obchodníci</option>
                            <?php foreach ($sales_reps as $rep): ?>
                            <option value="<?= htmlspecialchars($rep['code']) ?>" 
                                    <?= $filters['sales_rep'] === $rep['code'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($rep['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="technology" class="form-label">Technologie</label>
                        <select name="technology" id="technology" class="form-select">
                            <option value="">Všechny technologie</option>
                            <?php foreach ($technologies as $tech): ?>
                            <option value="<?= htmlspecialchars($tech) ?>" 
                                    <?= $filters['technology'] === $tech ? 'selected' : '' ?>>
                                <?= htmlspecialchars($tech) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="search" class="form-label">Hledat objednávku</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               value="<?= htmlspecialchars($filters['search']) ?>" 
                               placeholder="Kód objednávky...">
                    </div>
                    
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i>Filtrovat
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Calendar Grid -->
        <div class="card">
            <div class="card-body p-0">
                <div id="calendar-container">
                    <!-- Calendar content will be loaded here via AJAX -->
                    <div class="text-center p-5">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                        <p>Načítám kalendář...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/calendar.js"></script>
    
    <script>
        // Initialize calendar with current parameters
        $(document).ready(function() {
            loadCalendarData({
                month: <?= $current_month ?>,
                year: <?= $current_year ?>,
                view: '<?= $view_type ?>',
                sales_rep: '<?= htmlspecialchars($filters['sales_rep']) ?>',
                technology: '<?= htmlspecialchars($filters['technology']) ?>',
                search: '<?= htmlspecialchars($filters['search']) ?>'
            });
        });
    </script>
</body>
</html>
