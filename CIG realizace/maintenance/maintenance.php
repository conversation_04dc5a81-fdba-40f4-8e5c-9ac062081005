<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Údržba systému</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .maintenance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }
        
        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .maintenance-title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
        }
        
        .maintenance-subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .maintenance-details {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            text-align: left;
        }
        
        .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }
        
        .detail-item:last-child {
            margin-bottom: 0;
        }
        
        .detail-icon {
            color: #667eea;
            margin-right: 15px;
            width: 20px;
        }
        
        .progress-container {
            margin: 30px 0;
        }
        
        .progress {
            height: 10px;
            border-radius: 5px;
            background: #e9ecef;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 5px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 30%; }
            50% { width: 70%; }
            100% { width: 30%; }
        }
        
        .contact-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .contact-title {
            color: #856404;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .contact-details {
            color: #856404;
        }
        
        .estimated-time {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .time-title {
            color: #0c5460;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .time-details {
            color: #0c5460;
            font-size: 1.1rem;
        }
        
        .refresh-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            margin-top: 30px;
            transition: all 0.3s ease;
        }
        
        .refresh-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            background: #ffc107;
            border-radius: 50%;
            margin-right: 10px;
            animation: blink 1.5s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-subtitle {
                font-size: 1rem;
            }
            
            .detail-item {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">
            <span class="status-indicator"></span>
            Údržba systému
        </h1>
        
        <p class="maintenance-subtitle">
            Systém CIG Realizace je momentálně nedostupný z důvodu plánované údržby. 
            Pracujeme na vylepšení výkonu a přidání nových funkcí.
        </p>
        
        <div class="maintenance-details">
            <div class="detail-item">
                <i class="fas fa-clock detail-icon"></i>
                <span><strong>Začátek údržby:</strong> <?php echo date('d.m.Y H:i'); ?></span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-cog detail-icon"></i>
                <span><strong>Typ údržby:</strong> Aktualizace systému a optimalizace databáze</span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-server detail-icon"></i>
                <span><strong>Ovlivněné služby:</strong> Webová aplikace, API, email notifikace</span>
            </div>
            
            <div class="detail-item">
                <i class="fas fa-shield-alt detail-icon"></i>
                <span><strong>Bezpečnost dat:</strong> Všechna data jsou v bezpečí a zálohována</span>
            </div>
        </div>
        
        <div class="progress-container">
            <div class="progress">
                <div class="progress-bar" role="progressbar"></div>
            </div>
            <small class="text-muted mt-2 d-block">Probíhá aktualizace systému...</small>
        </div>
        
        <div class="estimated-time">
            <div class="time-title">
                <i class="fas fa-hourglass-half me-2"></i>
                Odhadovaný čas dokončení
            </div>
            <div class="time-details">
                <?php 
                // Estimate completion time (2 hours from now)
                $estimatedEnd = date('d.m.Y H:i', strtotime('+2 hours'));
                echo $estimatedEnd;
                ?>
            </div>
        </div>
        
        <div class="contact-info">
            <div class="contact-title">
                <i class="fas fa-phone me-2"></i>
                Potřebujete pomoc?
            </div>
            <div class="contact-details">
                V případě naléhavých záležitostí nás kontaktujte:<br>
                <strong>Email:</strong> <EMAIL><br>
                <strong>Telefon:</strong> +420 XXX XXX XXX
            </div>
        </div>
        
        <button class="btn refresh-button" onclick="location.reload()">
            <i class="fas fa-sync-alt me-2"></i>
            Obnovit stránku
        </button>
        
        <div class="mt-4">
            <small class="text-muted">
                Děkujeme za pochopení. Systém bude brzy opět k dispozici.
            </small>
        </div>
    </div>

    <script>
        // Auto-refresh every 5 minutes
        setTimeout(function() {
            location.reload();
        }, 300000);
        
        // Update progress bar animation
        document.addEventListener('DOMContentLoaded', function() {
            const progressBar = document.querySelector('.progress-bar');
            let progress = 30;
            
            setInterval(function() {
                progress += Math.random() * 10;
                if (progress > 90) progress = 30;
                progressBar.style.width = progress + '%';
            }, 5000);
        });
        
        // Add some interactivity
        document.querySelector('.refresh-button').addEventListener('click', function() {
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Obnovování...';
            setTimeout(function() {
                location.reload();
            }, 1000);
        });
    </script>
</body>
</html>

<?php
/**
 * Maintenance Mode Controller
 * 
 * To enable maintenance mode:
 * 1. Rename this file to index.php in the root directory
 * 2. Rename the original index.php to index_backup.php
 * 
 * To disable maintenance mode:
 * 1. Rename index_backup.php back to index.php
 * 2. Remove or rename this maintenance file
 * 
 * Or use the maintenance toggle script:
 * php maintenance/toggle_maintenance.php on
 * php maintenance/toggle_maintenance.php off
 */

// Log maintenance page access
$logEntry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? '/',
    'action' => 'maintenance_page_accessed'
];

$logFile = __DIR__ . '/../logs/maintenance.log';
file_put_contents($logFile, json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);

// Set appropriate HTTP status code
http_response_code(503);
header('Retry-After: 7200'); // 2 hours
?>
