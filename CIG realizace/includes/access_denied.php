<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIG Realizace - Přístup odepřen</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .error-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .error-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            text-align: center;
            max-width: 500px;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-card">
            <i class="fas fa-ban fa-4x text-danger mb-4"></i>
            <h1 class="h2 mb-3">Přístup odepřen</h1>
            <p class="text-muted mb-4">
                Nemáte oprávnění pro přístup k této stránce.<br>
                Kontaktujte administrátora systému pro získání potřebných oprávnění.
            </p>
            
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="dashboard.php" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>Zpět na dashboard
                </a>
                <a href="logout.php" class="btn btn-outline-secondary">
                    <i class="fas fa-sign-out-alt me-2"></i>Odhlásit se
                </a>
            </div>
            
            <?php if (isset($_SESSION['username'])): ?>
            <div class="mt-4 pt-3 border-top">
                <small class="text-muted">
                    Přihlášen jako: <strong><?= e($_SESSION['full_name'] ?? $_SESSION['username']) ?></strong><br>
                    Role: <strong><?= e(getRoleDisplayName($_SESSION['role'])) ?></strong>
                </small>
            </div>
            <?php endif; ?>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
