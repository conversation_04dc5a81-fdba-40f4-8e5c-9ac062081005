<?php
/**
 * Order Management Functions
 * CIG Realizace - Phase 04
 */

/**
 * Get pending orders with pagination
 */
function getPendingOrders($page = 1, $per_page = 25, $sort_by = 'created_at', $sort_order = 'DESC', $filters = []) {
    $pdo = getDbConnection();
    
    // Build WHERE clause
    $where_conditions = ["o.preview_status != 'approved'", "o.is_completed = 0"];
    $params = [];
    
    // Apply filters
    if (!empty($filters['sales_rep'])) {
        $where_conditions[] = "o.sales_rep = ?";
        $params[] = $filters['sales_rep'];
    }
    
    if (!empty($filters['preview_status'])) {
        $where_conditions[] = "o.preview_status = ?";
        $params[] = $filters['preview_status'];
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "o.order_date >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "o.order_date <= ?";
        $params[] = $filters['date_to'];
    }
    
    // Enhanced search filter
    if (!empty($filters['search'])) {
        $search_term = '%' . $filters['search'] . '%';
        $where_conditions[] = "(
            o.order_code LIKE ? OR
            o.sales_rep_name LIKE ? OR
            o.customer_name LIKE ? OR
            EXISTS (
                SELECT 1 FROM order_items oi
                WHERE oi.order_id = o.id
                  AND oi.is_relevant = 1
                  AND (oi.catalog_code LIKE ? OR oi.technology_assignment LIKE ?)
            )
        )";
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term, $search_term]);
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Validate sort parameters
    $allowed_sort_columns = ['order_code', 'order_date', 'sales_rep', 'preview_status', 'created_at', 'items_count'];
    if (!in_array($sort_by, $allowed_sort_columns)) {
        $sort_by = 'created_at';
    }
    
    $sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';
    
    // Get total count
    $count_sql = "
        SELECT COUNT(DISTINCT o.id) as total
        FROM orders o
        WHERE $where_clause
    ";
    
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_orders = $stmt->fetch()['total'];
    
    // Calculate pagination
    $total_pages = ceil($total_orders / $per_page);
    $offset = ($page - 1) * $per_page;
    
    // Get orders with items count
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as items_count,
               GROUP_CONCAT(DISTINCT oi.catalog_code) as catalog_codes
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        WHERE $where_clause
        GROUP BY o.id
        ORDER BY $sort_by $sort_order
        LIMIT $per_page OFFSET $offset
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    return [
        'orders' => $orders,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_orders' => $total_orders,
            'per_page' => $per_page,
            'has_prev' => $page > 1,
            'has_next' => $page < $total_pages
        ]
    ];
}

/**
 * Get all orders with pagination (for admin)
 */
function getAllOrders($page = 1, $per_page = 25, $sort_by = 'created_at', $sort_order = 'DESC', $filters = []) {
    $pdo = getDbConnection();
    
    // Build WHERE clause
    $where_conditions = ["1=1"]; // Always true condition
    $params = [];
    
    // Apply filters
    if (!empty($filters['sales_rep'])) {
        $where_conditions[] = "o.sales_rep = ?";
        $params[] = $filters['sales_rep'];
    }
    
    if (!empty($filters['status'])) {
        $where_conditions[] = "o.status = ?";
        $params[] = $filters['status'];
    }
    
    if (!empty($filters['preview_status'])) {
        $where_conditions[] = "o.preview_status = ?";
        $params[] = $filters['preview_status'];
    }
    
    if (!empty($filters['date_from'])) {
        $where_conditions[] = "o.order_date >= ?";
        $params[] = $filters['date_from'];
    }
    
    if (!empty($filters['date_to'])) {
        $where_conditions[] = "o.order_date <= ?";
        $params[] = $filters['date_to'];
    }
    
    // Enhanced search filter
    if (!empty($filters['search'])) {
        $search_term = '%' . $filters['search'] . '%';
        $where_conditions[] = "(
            o.order_code LIKE ? OR
            o.sales_rep_name LIKE ? OR
            o.customer_name LIKE ? OR
            EXISTS (
                SELECT 1 FROM order_items oi
                WHERE oi.order_id = o.id
                  AND oi.is_relevant = 1
                  AND (oi.catalog_code LIKE ? OR oi.technology_assignment LIKE ?)
            )
        )";
        $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term, $search_term]);
    }
    
    if (isset($filters['is_completed'])) {
        $where_conditions[] = "o.is_completed = ?";
        $params[] = $filters['is_completed'] ? 1 : 0;
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    // Validate sort parameters
    $allowed_sort_columns = ['order_code', 'order_date', 'sales_rep', 'status', 'preview_status', 'created_at', 'items_count'];
    if (!in_array($sort_by, $allowed_sort_columns)) {
        $sort_by = 'created_at';
    }
    
    $sort_order = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';
    
    // Get total count
    $count_sql = "
        SELECT COUNT(DISTINCT o.id) as total
        FROM orders o
        WHERE $where_clause
    ";
    
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_orders = $stmt->fetch()['total'];
    
    // Calculate pagination
    $total_pages = ceil($total_orders / $per_page);
    $offset = ($page - 1) * $per_page;
    
    // Get orders with items count
    $sql = "
        SELECT o.*, 
               COUNT(oi.id) as items_count,
               GROUP_CONCAT(DISTINCT oi.catalog_code) as catalog_codes
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        WHERE $where_clause
        GROUP BY o.id
        ORDER BY $sort_by $sort_order
        LIMIT $per_page OFFSET $offset
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $orders = $stmt->fetchAll();
    
    return [
        'orders' => $orders,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_orders' => $total_orders,
            'per_page' => $per_page,
            'has_prev' => $page > 1,
            'has_next' => $page < $total_pages
        ]
    ];
}

/**
 * Get order statistics
 */
function getOrderStatistics() {
    $pdo = getDbConnection();
    
    $stats = [];
    
    // Total orders
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM orders");
    $stats['total'] = $stmt->fetch()['total'] ?? 0;
    
    // Pending orders
    $stmt = $pdo->query("SELECT COUNT(*) as pending FROM orders WHERE preview_status != 'approved' AND is_completed = 0");
    $stats['pending'] = $stmt->fetch()['pending'] ?? 0;
    
    // In progress orders
    $stmt = $pdo->query("SELECT COUNT(*) as in_progress FROM orders WHERE status = 'in_progress'");
    $stats['in_progress'] = $stmt->fetch()['in_progress'] ?? 0;
    
    // Completed orders
    $stmt = $pdo->query("SELECT COUNT(*) as completed FROM orders WHERE is_completed = 1");
    $stats['completed'] = $stmt->fetch()['completed'] ?? 0;
    
    // Orders by preview status
    $stmt = $pdo->query("
        SELECT preview_status, COUNT(*) as count 
        FROM orders 
        WHERE is_completed = 0 
        GROUP BY preview_status
    ");
    $preview_stats = $stmt->fetchAll();
    
    $stats['by_preview_status'] = [];
    foreach ($preview_stats as $stat) {
        $stats['by_preview_status'][$stat['preview_status']] = $stat['count'];
    }
    
    // Orders by sales rep
    $stmt = $pdo->query("
        SELECT sales_rep, sales_rep_name, COUNT(*) as count 
        FROM orders 
        WHERE is_completed = 0 
        GROUP BY sales_rep, sales_rep_name
        ORDER BY count DESC
    ");
    $sales_stats = $stmt->fetchAll();
    $stats['by_sales_rep'] = $sales_stats;
    
    return $stats;
}

/**
 * Get available sales representatives
 */
function getSalesRepresentatives() {
    $pdo = getDbConnection();
    
    $stmt = $pdo->query("
        SELECT DISTINCT sales_rep, sales_rep_name 
        FROM orders 
        WHERE sales_rep IS NOT NULL 
        ORDER BY sales_rep_name
    ");
    
    return $stmt->fetchAll();
}

/**
 * Format preview status for display
 */
function formatPreviewStatus($status) {
    $statuses = [
        'not_created' => ['label' => 'Nevytvořen', 'class' => 'bg-danger'],
        'sent_to_client' => ['label' => 'Odeslán klientovi', 'class' => 'bg-warning'],
        'approved' => ['label' => 'Schválen', 'class' => 'bg-success']
    ];
    
    return $statuses[$status] ?? ['label' => ucfirst($status), 'class' => 'bg-secondary'];
}

/**
 * Format order status for display
 */
function formatOrderStatus($status) {
    $statuses = [
        'pending' => ['label' => 'Čeká', 'class' => 'bg-warning'],
        'in_progress' => ['label' => 'Ve zpracování', 'class' => 'bg-info'],
        'completed' => ['label' => 'Dokončeno', 'class' => 'bg-success'],
        'cancelled' => ['label' => 'Zrušeno', 'class' => 'bg-danger']
    ];
    
    return $statuses[$status] ?? ['label' => ucfirst($status), 'class' => 'bg-secondary'];
}

/**
 * Generate pagination HTML
 */
function renderPagination($pagination, $base_url, $params = []) {
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $html = '<nav aria-label="Stránkování objednávek">';
    $html .= '<ul class="pagination justify-content-center">';
    
    // Previous page
    if ($pagination['has_prev']) {
        $prev_params = array_merge($params, ['page' => $pagination['current_page'] - 1]);
        $prev_url = $base_url . '?' . http_build_query($prev_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $prev_url . '">Předchozí</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Předchozí</span></li>';
    }
    
    // Page numbers
    $start_page = max(1, $pagination['current_page'] - 2);
    $end_page = min($pagination['total_pages'], $pagination['current_page'] + 2);
    
    if ($start_page > 1) {
        $first_params = array_merge($params, ['page' => 1]);
        $first_url = $base_url . '?' . http_build_query($first_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $first_url . '">1</a></li>';
        if ($start_page > 2) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        if ($i == $pagination['current_page']) {
            $html .= '<li class="page-item active"><span class="page-link">' . $i . '</span></li>';
        } else {
            $page_params = array_merge($params, ['page' => $i]);
            $page_url = $base_url . '?' . http_build_query($page_params);
            $html .= '<li class="page-item"><a class="page-link" href="' . $page_url . '">' . $i . '</a></li>';
        }
    }
    
    if ($end_page < $pagination['total_pages']) {
        if ($end_page < $pagination['total_pages'] - 1) {
            $html .= '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        $last_params = array_merge($params, ['page' => $pagination['total_pages']]);
        $last_url = $base_url . '?' . http_build_query($last_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $last_url . '">' . $pagination['total_pages'] . '</a></li>';
    }
    
    // Next page
    if ($pagination['has_next']) {
        $next_params = array_merge($params, ['page' => $pagination['current_page'] + 1]);
        $next_url = $base_url . '?' . http_build_query($next_params);
        $html .= '<li class="page-item"><a class="page-link" href="' . $next_url . '">Další</a></li>';
    } else {
        $html .= '<li class="page-item disabled"><span class="page-link">Další</span></li>';
    }
    
    $html .= '</ul>';
    $html .= '</nav>';
    
    return $html;
}
