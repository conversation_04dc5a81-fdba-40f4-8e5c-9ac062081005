<?php
/**
 * Authentication Check Middleware
 * Include this file at the top of protected pages
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/../config/database.php';

/**
 * Simple function to check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Simple function to get current user
 */
function getCurrentUser() {
    if (isLoggedIn()) {
        return [
            'id' => $_SESSION['user_id'],
            'username' => $_SESSION['username'],
            'role' => $_SESSION['role'],
            'full_name' => $_SESSION['full_name'],
            'login_time' => $_SESSION['login_time'] ?? time()
        ];
    }
    return null;
}

// Check if user is logged in
if (!isLoggedIn()) {
    // Store the requested URL for redirect after login
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];

    // Determine the correct path to index.php
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    $current_file = basename($_SERVER['SCRIPT_NAME']);

    // If we're already on index.php, don't redirect
    if ($current_file === 'index.php') {
        return;
    }

    // Calculate relative path to root
    $path_parts = explode('/', trim($script_dir, '/'));
    $depth = count(array_filter($path_parts));
    $relative_path = str_repeat('../', $depth);

    // Redirect to login page
    header('Location: ' . $relative_path . 'index.php');
    exit;
}

// Update last activity time
$_SESSION['last_activity'] = time();
?>
