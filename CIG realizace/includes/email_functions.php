<?php
/**
 * Email Helper Functions
 * CIG Realizace - Phase 08
 */

require_once __DIR__ . '/../config/email.php';
require_once __DIR__ . '/../classes/EmailManager.php';
require_once __DIR__ . '/../classes/NotificationQueue.php';

/**
 * Send order completion notification
 */
function sendOrderCompletionEmail($orderId, $salesRepEmail = null) {
    try {
        $emailManager = new EmailManager();
        return $emailManager->sendOrderCompletionNotification($orderId, $salesRepEmail);
    } catch (Exception $e) {
        error_log("Error sending order completion email: " . $e->getMessage());
        return false;
    }
}

/**
 * Send preview status change notification
 */
function sendPreviewStatusChangeEmail($orderId, $oldStatus, $newStatus) {
    try {
        $emailManager = new EmailManager();
        return $emailManager->sendPreviewStatusNotification($orderId, $oldStatus, $newStatus);
    } catch (Exception $e) {
        error_log("Error sending preview status change email: " . $e->getMessage());
        return false;
    }
}

/**
 * Send overdue orders alert
 */
function sendOverdueOrdersAlert() {
    try {
        $overdueOrders = getOverdueOrders();
        
        if (empty($overdueOrders)) {
            return false;
        }
        
        $emailManager = new EmailManager();
        return $emailManager->sendOverdueAlert($overdueOrders);
    } catch (Exception $e) {
        error_log("Error sending overdue orders alert: " . $e->getMessage());
        return false;
    }
}

/**
 * Send daily summary to all users who have it enabled
 */
function sendDailySummaryEmails() {
    try {
        $emailManager = new EmailManager();
        return $emailManager->sendDailySummary();
    } catch (Exception $e) {
        error_log("Error sending daily summary emails: " . $e->getMessage());
        return false;
    }
}

/**
 * Send daily summary to specific user
 */
function sendDailySummaryToUser($userId) {
    try {
        $emailManager = new EmailManager();
        return $emailManager->sendDailySummary($userId);
    } catch (Exception $e) {
        error_log("Error sending daily summary to user: " . $e->getMessage());
        return false;
    }
}

/**
 * Get overdue orders
 */
function getOverdueOrders() {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            SELECT o.*, 
                   GROUP_CONCAT(DISTINCT oi.technology) as technologies,
                   COUNT(oi.id) as items_count
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE o.expected_delivery_date < DATE('now')
            AND o.is_completed = 0
            AND o.preview_status = 'approved'
            GROUP BY o.id
            ORDER BY o.expected_delivery_date ASC
        ");
        
        $stmt->execute();
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log("Error getting overdue orders: " . $e->getMessage());
        return [];
    }
}

/**
 * Process email queue
 */
function processEmailQueue($batchSize = null) {
    try {
        $queue = new NotificationQueue();
        return $queue->processPendingEmails($batchSize);
    } catch (Exception $e) {
        error_log("Error processing email queue: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get email queue statistics
 */
function getEmailQueueStats() {
    try {
        $queue = new NotificationQueue();
        return $queue->getQueueStatistics();
    } catch (Exception $e) {
        error_log("Error getting email queue stats: " . $e->getMessage());
        return [];
    }
}

/**
 * Clean old emails from queue
 */
function cleanOldEmailQueue($daysOld = 30) {
    try {
        $queue = new NotificationQueue();
        return $queue->cleanOldEmails($daysOld);
    } catch (Exception $e) {
        error_log("Error cleaning old email queue: " . $e->getMessage());
        return 0;
    }
}

/**
 * Test email configuration
 */
function testEmailConfiguration() {
    try {
        $config = getEmailConfig();
        
        $tests = [
            'smtp_configured' => !empty($config['smtp_host']) && !empty($config['from_email']),
            'templates_exist' => checkEmailTemplatesExist(),
            'database_tables' => checkEmailDatabaseTables(),
            'queue_writable' => testEmailQueueWrite()
        ];
        
        return $tests;
        
    } catch (Exception $e) {
        error_log("Error testing email configuration: " . $e->getMessage());
        return [
            'smtp_configured' => false,
            'templates_exist' => false,
            'database_tables' => false,
            'queue_writable' => false
        ];
    }
}

/**
 * Check if email templates exist
 */
function checkEmailTemplatesExist() {
    $templateDir = getEmailTemplateDir();
    $requiredTemplates = [
        'order_completed.php',
        'preview_status_changed.php',
        'overdue_alert.php',
        'daily_summary.php'
    ];
    
    foreach ($requiredTemplates as $template) {
        if (!file_exists($templateDir . $template)) {
            return false;
        }
    }
    
    return true;
}

/**
 * Check if email database tables exist
 */
function checkEmailDatabaseTables() {
    try {
        $pdo = getDbConnection();
        
        $requiredTables = [
            'notification_queue',
            'user_notification_preferences',
            'email_delivery_tracking',
            'email_statistics'
        ];
        
        foreach ($requiredTables as $table) {
            $stmt = $pdo->prepare("SELECT name FROM sqlite_master WHERE type='table' AND name=?");
            $stmt->execute([$table]);
            
            if (!$stmt->fetch()) {
                return false;
            }
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Error checking email database tables: " . $e->getMessage());
        return false;
    }
}

/**
 * Test email queue write functionality
 */
function testEmailQueueWrite() {
    try {
        $queue = new NotificationQueue();
        
        // Try to queue a test email
        $testId = $queue->queueEmail(
            '<EMAIL>',
            'Test User',
            'Test Email',
            'order_completed',
            ['test' => true],
            10 // Low priority
        );
        
        if ($testId) {
            // Clean up test email
            $pdo = getDbConnection();
            $stmt = $pdo->prepare("DELETE FROM notification_queue WHERE id = ?");
            $stmt->execute([$testId]);
            
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Error testing email queue write: " . $e->getMessage());
        return false;
    }
}

/**
 * Get user notification preferences
 */
function getUserNotificationPreferences($userId) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            SELECT * FROM user_notification_preferences 
            WHERE user_id = ?
        ");
        
        $stmt->execute([$userId]);
        $prefs = $stmt->fetch();
        
        // Return default preferences if none exist
        if (!$prefs) {
            return [
                'user_id' => $userId,
                'order_completion' => 1,
                'preview_status_change' => 1,
                'overdue_alerts' => 1,
                'daily_summary' => 0,
                'weekly_summary' => 1,
                'email_format' => 'html',
                'frequency' => 'immediate'
            ];
        }
        
        return $prefs;
        
    } catch (Exception $e) {
        error_log("Error getting user notification preferences: " . $e->getMessage());
        return null;
    }
}

/**
 * Update user notification preferences
 */
function updateUserNotificationPreferences($userId, $preferences) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            INSERT INTO user_notification_preferences (
                user_id, order_completion, preview_status_change, 
                overdue_alerts, daily_summary, weekly_summary, 
                email_format, frequency, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            ON CONFLICT(user_id) DO UPDATE SET
                order_completion = excluded.order_completion,
                preview_status_change = excluded.preview_status_change,
                overdue_alerts = excluded.overdue_alerts,
                daily_summary = excluded.daily_summary,
                weekly_summary = excluded.weekly_summary,
                email_format = excluded.email_format,
                frequency = excluded.frequency,
                updated_at = CURRENT_TIMESTAMP
        ");
        
        return $stmt->execute([
            $userId,
            $preferences['order_completion'] ?? 1,
            $preferences['preview_status_change'] ?? 1,
            $preferences['overdue_alerts'] ?? 1,
            $preferences['daily_summary'] ?? 0,
            $preferences['weekly_summary'] ?? 1,
            $preferences['email_format'] ?? 'html',
            $preferences['frequency'] ?? 'immediate'
        ]);
        
    } catch (Exception $e) {
        error_log("Error updating user notification preferences: " . $e->getMessage());
        return false;
    }
}

/**
 * Get email delivery statistics
 */
function getEmailDeliveryStats($days = 30) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            SELECT 
                DATE(nq.created_at) as date,
                COUNT(*) as total_sent,
                COUNT(edt.delivered_at) as delivered,
                COUNT(edt.opened_at) as opened,
                COUNT(edt.clicked_at) as clicked,
                COUNT(edt.bounced_at) as bounced
            FROM notification_queue nq
            LEFT JOIN email_delivery_tracking edt ON nq.id = edt.queue_id
            WHERE nq.status = 'sent'
            AND nq.created_at >= datetime('now', '-' || ? || ' days')
            GROUP BY DATE(nq.created_at)
            ORDER BY date DESC
        ");
        
        $stmt->execute([$days]);
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log("Error getting email delivery stats: " . $e->getMessage());
        return [];
    }
}

/**
 * Track email open
 */
function trackEmailOpen($queueId, $userAgent = null, $ipAddress = null) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            UPDATE email_delivery_tracking 
            SET opened_at = CURRENT_TIMESTAMP,
                user_agent = ?,
                ip_address = ?
            WHERE queue_id = ? AND opened_at IS NULL
        ");
        
        return $stmt->execute([
            $userAgent,
            $ipAddress,
            $queueId
        ]);
        
    } catch (Exception $e) {
        error_log("Error tracking email open: " . $e->getMessage());
        return false;
    }
}

/**
 * Track email click
 */
function trackEmailClick($queueId, $userAgent = null, $ipAddress = null) {
    try {
        $pdo = getDbConnection();
        
        $stmt = $pdo->prepare("
            UPDATE email_delivery_tracking 
            SET clicked_at = CURRENT_TIMESTAMP,
                user_agent = ?,
                ip_address = ?
            WHERE queue_id = ?
        ");
        
        return $stmt->execute([
            $userAgent,
            $ipAddress,
            $queueId
        ]);
        
    } catch (Exception $e) {
        error_log("Error tracking email click: " . $e->getMessage());
        return false;
    }
}
?>
