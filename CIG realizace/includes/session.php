<?php
/**
 * Session Management
 * Secure session configuration and initialization
 */

// Secure session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// Set session name
session_name('CIG_REALIZACE_SESSION');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include required classes
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/User.php';
require_once __DIR__ . '/../classes/Auth.php';

// Initialize authentication
try {
    $pdo = getDbConnection();
    $auth = new Auth($pdo);
} catch (Exception $e) {
    // Handle database connection error
    error_log('Database connection error in session.php: ' . $e->getMessage());
    $auth = null;
}

/**
 * Helper function to check if user is logged in
 */
function isLoggedIn() {
    global $auth;
    return $auth && $auth->isLoggedIn();
}

/**
 * Helper function to get current user
 */
function getCurrentUser() {
    global $auth;
    return $auth ? $auth->getCurrentUser() : null;
}

/**
 * Helper function to check user role
 */
function hasRole($role) {
    global $auth;
    return $auth && $auth->hasRole($role);
}

/**
 * Helper function to check multiple roles
 */
function hasAnyRole($roles) {
    global $auth;
    return $auth && $auth->hasAnyRole($roles);
}

/**
 * Helper function to require login
 */
function requireLogin() {
    global $auth;
    if ($auth) {
        $auth->requireLogin();
    } else {
        header('Location: index.php');
        exit;
    }
}

/**
 * Helper function to require specific role
 */
function requireRole($role) {
    global $auth;
    if ($auth) {
        $auth->requireRole($role);
    } else {
        header('Location: index.php');
        exit;
    }
}

/**
 * Helper function to require any of specified roles
 */
function requireAnyRole($roles) {
    global $auth;
    if ($auth) {
        $auth->requireAnyRole($roles);
    } else {
        header('Location: index.php');
        exit;
    }
}

/**
 * Helper function to generate CSRF token
 */
function getCSRFToken() {
    global $auth;
    return $auth ? $auth->generateCSRFToken() : '';
}

/**
 * Helper function to verify CSRF token
 */
function verifyCSRFToken($token) {
    global $auth;
    return $auth && $auth->verifyCSRFToken($token);
}

/**
 * Helper function to check permission
 */
function hasPermission($permission) {
    global $auth;
    return $auth && $auth->hasPermission($permission);
}

/**
 * Helper function to get role display name
 */
function getRoleDisplayName($role = null) {
    global $pdo;
    if ($pdo) {
        $user = new User($pdo);
        return $user->getRoleDisplayName($role ?: ($_SESSION['role'] ?? ''));
    }
    return $role;
}

/**
 * Helper function to format date
 */
function formatDate($date, $format = 'd.m.Y') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Helper function to format datetime
 */
function formatDateTime($datetime, $format = 'd.m.Y H:i') {
    if (empty($datetime)) return '';
    return date($format, strtotime($datetime));
}

/**
 * Helper function to escape HTML
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Helper function to show flash messages
 */
function showFlashMessage($type = null) {
    $messages = [
        'success' => $_SESSION['success_message'] ?? null,
        'error' => $_SESSION['error_message'] ?? null,
        'warning' => $_SESSION['warning_message'] ?? null,
        'info' => $_SESSION['info_message'] ?? null,
        'logout' => $_SESSION['logout_message'] ?? null
    ];
    
    if ($type && isset($messages[$type])) {
        $message = $messages[$type];
        unset($_SESSION[$type . '_message']);
        return $message;
    }
    
    foreach ($messages as $msg_type => $message) {
        if ($message) {
            unset($_SESSION[$msg_type . '_message']);
            $class = $msg_type === 'error' ? 'danger' : $msg_type;
            if ($msg_type === 'logout') $class = 'info';
            
            echo '<div class="alert alert-' . $class . ' alert-dismissible fade show" role="alert">';
            echo '<i class="fas fa-' . ($msg_type === 'success' ? 'check-circle' : 
                                      ($msg_type === 'error' ? 'exclamation-circle' : 
                                       ($msg_type === 'warning' ? 'exclamation-triangle' : 'info-circle'))) . ' me-2"></i>';
            echo e($message);
            echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            echo '</div>';
            break; // Show only one message at a time
        }
    }
}

/**
 * Helper function to set flash message
 */
function setFlashMessage($type, $message) {
    $_SESSION[$type . '_message'] = $message;
}
?>
