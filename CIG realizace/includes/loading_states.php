<?php
/**
 * CIG Realizace - Loading States and UI Feedback
 * Helper functions for consistent loading states and user feedback
 */

class LoadingStates {
    
    /**
     * Generate loading spinner HTML
     */
    public static function spinner($message = 'Načítání...', $size = 'medium') {
        $sizeClass = '';
        switch($size) {
            case 'small':
                $sizeClass = 'loading-spinner-sm';
                break;
            case 'large':
                $sizeClass = 'loading-spinner-lg';
                break;
            default:
                $sizeClass = 'loading-spinner-md';
        }
        
        return '
        <div class="loading-overlay" role="status" aria-live="polite">
            <div class="loading-spinner ' . $sizeClass . '" aria-hidden="true"></div>
            <div class="loading-message">' . htmlspecialchars($message) . '</div>
            <span class="sr-only">Načítání obsahu...</span>
        </div>';
    }
    
    /**
     * Generate skeleton loader for tables
     */
    public static function tableSkeleton($rows = 5, $columns = 4) {
        $html = '<div class="skeleton-loader" aria-label="Načítání tabulky">';
        $html .= '<div class="skeleton-table">';
        
        // Header skeleton
        $html .= '<div class="skeleton-row skeleton-header">';
        for ($i = 0; $i < $columns; $i++) {
            $html .= '<div class="skeleton-cell skeleton-header-cell"></div>';
        }
        $html .= '</div>';
        
        // Body skeleton
        for ($r = 0; $r < $rows; $r++) {
            $html .= '<div class="skeleton-row">';
            for ($c = 0; $c < $columns; $c++) {
                $html .= '<div class="skeleton-cell"></div>';
            }
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate skeleton loader for cards
     */
    public static function cardSkeleton($count = 4) {
        $html = '<div class="skeleton-loader" aria-label="Načítání karet">';
        $html .= '<div class="skeleton-cards">';
        
        for ($i = 0; $i < $count; $i++) {
            $html .= '
            <div class="skeleton-card">
                <div class="skeleton-card-header"></div>
                <div class="skeleton-card-body">
                    <div class="skeleton-line skeleton-line-lg"></div>
                    <div class="skeleton-line skeleton-line-md"></div>
                    <div class="skeleton-line skeleton-line-sm"></div>
                </div>
            </div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate progress bar
     */
    public static function progressBar($percentage = 0, $label = '', $animated = true) {
        $animatedClass = $animated ? 'progress-bar-animated' : '';
        $ariaLabel = $label ? $label : 'Průběh: ' . $percentage . '%';
        
        return '
        <div class="progress" role="progressbar" aria-label="' . htmlspecialchars($ariaLabel) . '" aria-valuenow="' . $percentage . '" aria-valuemin="0" aria-valuemax="100">
            <div class="progress-bar ' . $animatedClass . '" style="width: ' . $percentage . '%">
                ' . ($label ? htmlspecialchars($label) : $percentage . '%') . '
            </div>
        </div>';
    }
    
    /**
     * Generate empty state message
     */
    public static function emptyState($title, $message, $actionButton = null, $icon = 'fas fa-inbox') {
        $html = '
        <div class="empty-state text-center py-5">
            <div class="empty-state-icon mb-3">
                <i class="' . $icon . ' fa-3x text-muted" aria-hidden="true"></i>
            </div>
            <h3 class="empty-state-title text-muted">' . htmlspecialchars($title) . '</h3>
            <p class="empty-state-message text-muted mb-4">' . htmlspecialchars($message) . '</p>';
        
        if ($actionButton) {
            $html .= '<div class="empty-state-action">' . $actionButton . '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate error state message
     */
    public static function errorState($title, $message, $retryButton = null) {
        $html = '
        <div class="error-state text-center py-5" role="alert">
            <div class="error-state-icon mb-3">
                <i class="fas fa-exclamation-triangle fa-3x text-danger" aria-hidden="true"></i>
            </div>
            <h3 class="error-state-title text-danger">' . htmlspecialchars($title) . '</h3>
            <p class="error-state-message text-muted mb-4">' . htmlspecialchars($message) . '</p>';
        
        if ($retryButton) {
            $html .= '<div class="error-state-action">' . $retryButton . '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate success state message
     */
    public static function successState($title, $message, $actionButton = null) {
        $html = '
        <div class="success-state text-center py-5" role="status">
            <div class="success-state-icon mb-3">
                <i class="fas fa-check-circle fa-3x text-success" aria-hidden="true"></i>
            </div>
            <h3 class="success-state-title text-success">' . htmlspecialchars($title) . '</h3>
            <p class="success-state-message text-muted mb-4">' . htmlspecialchars($message) . '</p>';
        
        if ($actionButton) {
            $html .= '<div class="success-state-action">' . $actionButton . '</div>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Generate loading button state
     */
    public static function loadingButton($text = 'Načítání...', $originalText = '', $disabled = true) {
        $disabledAttr = $disabled ? 'disabled' : '';
        $dataOriginal = $originalText ? 'data-original-text="' . htmlspecialchars($originalText) . '"' : '';
        
        return '
        <button type="button" class="btn btn-primary loading-btn" ' . $disabledAttr . ' ' . $dataOriginal . '>
            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
            ' . htmlspecialchars($text) . '
        </button>';
    }
    
    /**
     * Generate toast notification
     */
    public static function toast($message, $type = 'success', $title = '', $autoHide = true) {
        $toastId = 'toast-' . uniqid();
        $iconClass = self::getToastIcon($type);
        $bgClass = 'bg-' . $type;
        $autoHideAttr = $autoHide ? 'data-bs-autohide="true"' : 'data-bs-autohide="false"';
        
        $titleHtml = '';
        if ($title) {
            $titleHtml = '
            <div class="toast-header ' . $bgClass . ' text-white">
                <i class="' . $iconClass . ' me-2" aria-hidden="true"></i>
                <strong class="me-auto">' . htmlspecialchars($title) . '</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Zavřít"></button>
            </div>';
        }
        
        return '
        <div id="' . $toastId . '" class="toast" role="alert" aria-live="assertive" aria-atomic="true" ' . $autoHideAttr . '>
            ' . $titleHtml . '
            <div class="toast-body ' . ($title ? '' : $bgClass . ' text-white') . '">
                ' . ($title ? '' : '<i class="' . $iconClass . ' me-2" aria-hidden="true"></i>') . '
                ' . htmlspecialchars($message) . '
                ' . ($title ? '' : '<button type="button" class="btn-close btn-close-white ms-2 m-auto" data-bs-dismiss="toast" aria-label="Zavřít"></button>') . '
            </div>
        </div>';
    }
    
    /**
     * Get icon class for toast type
     */
    private static function getToastIcon($type) {
        $icons = [
            'success' => 'fas fa-check-circle',
            'error' => 'fas fa-exclamation-circle',
            'warning' => 'fas fa-exclamation-triangle',
            'info' => 'fas fa-info-circle',
            'danger' => 'fas fa-exclamation-circle'
        ];
        
        return $icons[$type] ?? 'fas fa-info-circle';
    }
    
    /**
     * Generate breadcrumb navigation
     */
    public static function breadcrumb($items) {
        if (empty($items)) {
            return '';
        }
        
        $html = '<nav aria-label="Navigace" class="breadcrumb-nav">';
        $html .= '<ol class="breadcrumb">';
        
        $lastIndex = count($items) - 1;
        foreach ($items as $index => $item) {
            $isLast = ($index === $lastIndex);
            
            if ($isLast) {
                $html .= '<li class="breadcrumb-item active" aria-current="page">' . htmlspecialchars($item['text']) . '</li>';
            } else {
                $html .= '<li class="breadcrumb-item">';
                if (isset($item['url'])) {
                    $html .= '<a href="' . htmlspecialchars($item['url']) . '">' . htmlspecialchars($item['text']) . '</a>';
                } else {
                    $html .= htmlspecialchars($item['text']);
                }
                $html .= '</li>';
            }
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
    
    /**
     * Generate pagination with accessibility
     */
    public static function pagination($currentPage, $totalPages, $baseUrl, $queryParams = []) {
        if ($totalPages <= 1) {
            return '';
        }
        
        $html = '<nav aria-label="Stránkování" class="pagination-nav">';
        $html .= '<ul class="pagination justify-content-center">';
        
        // Previous button
        $prevDisabled = ($currentPage <= 1) ? 'disabled' : '';
        $prevPage = max(1, $currentPage - 1);
        $prevUrl = $baseUrl . '?' . http_build_query(array_merge($queryParams, ['page' => $prevPage]));
        
        $html .= '<li class="page-item ' . $prevDisabled . '">';
        if ($prevDisabled) {
            $html .= '<span class="page-link" aria-label="Předchozí stránka">Předchozí</span>';
        } else {
            $html .= '<a class="page-link" href="' . htmlspecialchars($prevUrl) . '" aria-label="Předchozí stránka">Předchozí</a>';
        }
        $html .= '</li>';
        
        // Page numbers
        $startPage = max(1, $currentPage - 2);
        $endPage = min($totalPages, $currentPage + 2);
        
        for ($i = $startPage; $i <= $endPage; $i++) {
            $activeClass = ($i === $currentPage) ? 'active' : '';
            $pageUrl = $baseUrl . '?' . http_build_query(array_merge($queryParams, ['page' => $i]));
            
            $html .= '<li class="page-item ' . $activeClass . '">';
            if ($activeClass) {
                $html .= '<span class="page-link" aria-current="page" aria-label="Stránka ' . $i . ', aktuální">' . $i . '</span>';
            } else {
                $html .= '<a class="page-link" href="' . htmlspecialchars($pageUrl) . '" aria-label="Stránka ' . $i . '">' . $i . '</a>';
            }
            $html .= '</li>';
        }
        
        // Next button
        $nextDisabled = ($currentPage >= $totalPages) ? 'disabled' : '';
        $nextPage = min($totalPages, $currentPage + 1);
        $nextUrl = $baseUrl . '?' . http_build_query(array_merge($queryParams, ['page' => $nextPage]));
        
        $html .= '<li class="page-item ' . $nextDisabled . '">';
        if ($nextDisabled) {
            $html .= '<span class="page-link" aria-label="Další stránka">Další</span>';
        } else {
            $html .= '<a class="page-link" href="' . htmlspecialchars($nextUrl) . '" aria-label="Další stránka">Další</a>';
        }
        $html .= '</li>';
        
        $html .= '</ul>';
        $html .= '</nav>';
        
        return $html;
    }
}
?>
