<?php
/**
 * EmailManager Class - Handle email sending and templates
 * CIG Realizace - Phase 08
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/email.php';
require_once __DIR__ . '/NotificationQueue.php';

class EmailManager {
    
    private $config;
    private $queue;
    
    public function __construct() {
        $this->config = getEmailConfig();
        $this->queue = new NotificationQueue();
    }
    
    /**
     * Send order completion notification to sales representative
     */
    public function sendOrderCompletionNotification($orderId, $salesRepEmail = null) {
        try {
            // Get order data
            $order = $this->getOrderData($orderId);
            if (!$order) {
                throw new Exception("Order not found: $orderId");
            }
            
            // Get sales rep email if not provided
            if (!$salesRepEmail) {
                $salesRepEmail = $this->getSalesRepEmail($order['sales_rep']);
            }
            
            if (!$salesRepEmail) {
                throw new Exception("Sales representative email not found for order: $orderId");
            }
            
            // Check user preferences
            if (!$this->shouldSendNotification($salesRepEmail, 'order_completion')) {
                return false;
            }
            
            // Prepare template data
            $templateData = [
                'order_code' => $order['order_code'],
                'sales_rep_name' => $order['sales_rep_name'],
                'order_date' => $order['order_date'],
                'completion_date' => date('d.m.Y H:i'),
                'items_count' => $order['items_count'],
                'technologies' => $order['technologies'],
                'order_url' => $this->getOrderUrl($orderId)
            ];
            
            // Queue email
            return $this->queue->queueEmail(
                $salesRepEmail,
                $order['sales_rep_name'],
                'Objednávka dokončena - ' . $order['order_code'],
                'order_completed',
                $templateData,
                1 // High priority
            );
            
        } catch (Exception $e) {
            error_log("EmailManager sendOrderCompletionNotification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send preview status change notification
     */
    public function sendPreviewStatusNotification($orderId, $oldStatus, $newStatus) {
        try {
            // Get order data
            $order = $this->getOrderData($orderId);
            if (!$order) {
                throw new Exception("Order not found: $orderId");
            }
            
            // Get recipients based on status change
            $recipients = $this->getStatusChangeRecipients($order, $oldStatus, $newStatus);
            
            if (empty($recipients)) {
                return false;
            }
            
            // Prepare template data
            $templateData = [
                'order_code' => $order['order_code'],
                'old_status_text' => $this->getStatusText($oldStatus),
                'new_status_text' => $this->getStatusText($newStatus),
                'changed_by' => $_SESSION['user']['full_name'] ?? 'System',
                'change_date' => date('d.m.Y H:i'),
                'order_url' => $this->getOrderUrl($orderId)
            ];
            
            // Add delivery date if approved
            if ($newStatus === 'approved' && !empty($order['expected_delivery_date'])) {
                $templateData['approved'] = true;
                $templateData['delivery_date'] = date('d.m.Y', strtotime($order['expected_delivery_date']));
            }
            
            // Queue emails for all recipients
            $queued = 0;
            foreach ($recipients as $recipient) {
                if ($this->shouldSendNotification($recipient['email'], 'preview_status_change')) {
                    $personalizedData = $templateData;
                    $personalizedData['recipient_name'] = $recipient['name'];
                    
                    if ($this->queue->queueEmail(
                        $recipient['email'],
                        $recipient['name'],
                        'Změna stavu náhledu - ' . $order['order_code'],
                        'preview_status_changed',
                        $personalizedData,
                        3 // Medium priority
                    )) {
                        $queued++;
                    }
                }
            }
            
            return $queued > 0;
            
        } catch (Exception $e) {
            error_log("EmailManager sendPreviewStatusNotification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send overdue alert to administrators
     */
    public function sendOverdueAlert($overdueOrders) {
        try {
            if (empty($overdueOrders)) {
                return false;
            }
            
            // Get admin users
            $admins = $this->getAdminUsers();
            if (empty($admins)) {
                return false;
            }
            
            // Prepare template data
            $templateData = [
                'overdue_count' => count($overdueOrders),
                'orders' => $overdueOrders,
                'alert_date' => date('d.m.Y'),
                'dashboard_url' => $this->getDashboardUrl()
            ];
            
            // Queue emails for all admins
            $queued = 0;
            foreach ($admins as $admin) {
                if ($this->shouldSendNotification($admin['email'], 'overdue_alerts')) {
                    $personalizedData = $templateData;
                    $personalizedData['admin_name'] = $admin['full_name'];
                    
                    if ($this->queue->queueEmail(
                        $admin['email'],
                        $admin['full_name'],
                        'Upozornění na zpožděné objednávky - ' . count($overdueOrders) . ' objednávek',
                        'overdue_alert',
                        $personalizedData,
                        2 // High priority
                    )) {
                        $queued++;
                    }
                }
            }
            
            return $queued > 0;
            
        } catch (Exception $e) {
            error_log("EmailManager sendOverdueAlert error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send daily summary to users who have it enabled
     */
    public function sendDailySummary($userId = null) {
        try {
            // Get users who should receive daily summary
            $users = $this->getUsersForDailySummary($userId);
            
            if (empty($users)) {
                return false;
            }
            
            $queued = 0;
            foreach ($users as $user) {
                // Get user's daily statistics
                $stats = $this->getDailyStats($user['id']);
                
                // Skip if no activity
                if ($stats['total_activity'] == 0) {
                    continue;
                }
                
                // Prepare template data
                $templateData = [
                    'user_name' => $user['full_name'],
                    'date' => date('d.m.Y'),
                    'new_orders' => $stats['new_orders'],
                    'completed_orders' => $stats['completed_orders'],
                    'pending_previews' => $stats['pending_previews'],
                    'overdue_orders' => $stats['overdue_orders'],
                    'total_activity' => $stats['total_activity'],
                    'dashboard_url' => $this->getDashboardUrl()
                ];
                
                if ($this->queue->queueEmail(
                    $user['email'],
                    $user['full_name'],
                    'Denní souhrn - ' . date('d.m.Y'),
                    'daily_summary',
                    $templateData,
                    5 // Normal priority
                )) {
                    $queued++;
                    
                    // Update last daily summary timestamp
                    $this->updateLastSummaryTimestamp($user['id'], 'daily');
                }
            }
            
            return $queued;
            
        } catch (Exception $e) {
            error_log("EmailManager sendDailySummary error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Queue email for sending
     */
    public function queueEmail($to, $subject, $template, $data, $priority = 5) {
        return $this->queue->queueEmail($to, null, $subject, $template, $data, $priority);
    }
    
    /**
     * Send email immediately (bypass queue)
     */
    public function sendEmailNow($to, $toName, $subject, $template, $data) {
        try {
            // Load and compile template
            $emailContent = $this->compileTemplate($template, $data);
            
            if (!$emailContent) {
                throw new Exception("Failed to compile template: $template");
            }
            
            // Send email using SMTP
            return $this->sendSMTPEmail($to, $toName, $subject, $emailContent['html'], $emailContent['text']);
            
        } catch (Exception $e) {
            error_log("EmailManager sendEmailNow error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get order data for email templates
     */
    private function getOrderData($orderId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                SELECT o.*, 
                       COUNT(oi.id) as items_count,
                       GROUP_CONCAT(DISTINCT oi.technology) as technologies,
                       u.full_name as sales_rep_name
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id
                LEFT JOIN users u ON o.sales_rep = u.username
                WHERE o.id = ?
                GROUP BY o.id
            ");
            
            $stmt->execute([$orderId]);
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Error getting order data: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Check if user should receive notification type
     */
    private function shouldSendNotification($email, $notificationType) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                SELECT unp.*
                FROM user_notification_preferences unp
                JOIN users u ON unp.user_id = u.id
                WHERE u.email = ? AND unp.$notificationType = 1
            ");
            
            $stmt->execute([$email]);
            return $stmt->fetch() !== false;
            
        } catch (Exception $e) {
            error_log("Error checking notification preferences: " . $e->getMessage());
            return true; // Default to sending if can't check preferences
        }
    }
    
    /**
     * Get sales representative email
     */
    private function getSalesRepEmail($salesRep) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("SELECT email FROM users WHERE username = ? AND role = 'obchodnik'");
            $stmt->execute([$salesRep]);
            
            $result = $stmt->fetch();
            return $result ? $result['email'] : null;
            
        } catch (Exception $e) {
            error_log("Error getting sales rep email: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get order URL for email links
     */
    private function getOrderUrl($orderId) {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/orders/detail.php?id=' . $orderId;
    }
    
    /**
     * Get dashboard URL
     */
    private function getDashboardUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return $protocol . '://' . $host . '/dashboard.php';
    }

    /**
     * Get recipients for status change notifications
     */
    private function getStatusChangeRecipients($order, $oldStatus, $newStatus) {
        try {
            $pdo = getDbConnection();
            $recipients = [];

            // Always notify sales rep
            if (!empty($order['sales_rep'])) {
                $stmt = $pdo->prepare("SELECT email, full_name FROM users WHERE username = ?");
                $stmt->execute([$order['sales_rep']]);
                $salesRep = $stmt->fetch();

                if ($salesRep) {
                    $recipients[] = [
                        'email' => $salesRep['email'],
                        'name' => $salesRep['full_name']
                    ];
                }
            }

            // Notify admins for important status changes
            if ($newStatus === 'approved' || $newStatus === 'rejected') {
                $stmt = $pdo->prepare("SELECT email, full_name FROM users WHERE role = 'admin' AND is_active = 1");
                $stmt->execute();

                while ($admin = $stmt->fetch()) {
                    $recipients[] = [
                        'email' => $admin['email'],
                        'name' => $admin['full_name']
                    ];
                }
            }

            return $recipients;

        } catch (Exception $e) {
            error_log("Error getting status change recipients: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get admin users
     */
    private function getAdminUsers() {
        try {
            $pdo = getDbConnection();

            $stmt = $pdo->prepare("SELECT email, full_name FROM users WHERE role = 'admin' AND is_active = 1");
            $stmt->execute();

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Error getting admin users: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get users for daily summary
     */
    private function getUsersForDailySummary($userId = null) {
        try {
            $pdo = getDbConnection();

            $sql = "
                SELECT u.id, u.email, u.full_name
                FROM users u
                JOIN user_notification_preferences unp ON u.id = unp.user_id
                WHERE u.is_active = 1 AND unp.daily_summary = 1
                AND (unp.last_daily_summary IS NULL OR DATE(unp.last_daily_summary) < DATE('now'))
            ";

            $params = [];
            if ($userId) {
                $sql .= " AND u.id = ?";
                $params[] = $userId;
            }

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();

        } catch (Exception $e) {
            error_log("Error getting users for daily summary: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get daily statistics for user
     */
    private function getDailyStats($userId) {
        try {
            $pdo = getDbConnection();

            // Get today's statistics
            $stmt = $pdo->prepare("
                SELECT
                    COUNT(CASE WHEN oh.action = 'order_created' AND DATE(oh.created_at) = DATE('now') THEN 1 END) as new_orders,
                    COUNT(CASE WHEN oh.action = 'order_completed' AND DATE(oh.created_at) = DATE('now') THEN 1 END) as completed_orders,
                    COUNT(CASE WHEN oh.action = 'preview_status_change' AND DATE(oh.created_at) = DATE('now') THEN 1 END) as pending_previews,
                    COUNT(CASE WHEN DATE(oh.created_at) = DATE('now') THEN 1 END) as total_activity
                FROM order_history oh
                WHERE oh.user_id = ?
            ");

            $stmt->execute([$userId]);
            $stats = $stmt->fetch();

            // Get overdue orders count
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as overdue_orders
                FROM orders o
                WHERE o.sales_rep = (SELECT username FROM users WHERE id = ?)
                AND o.expected_delivery_date < DATE('now')
                AND o.is_completed = 0
            ");

            $stmt->execute([$userId]);
            $overdue = $stmt->fetch();

            $stats['overdue_orders'] = $overdue['overdue_orders'];

            return $stats;

        } catch (Exception $e) {
            error_log("Error getting daily stats: " . $e->getMessage());
            return [
                'new_orders' => 0,
                'completed_orders' => 0,
                'pending_previews' => 0,
                'overdue_orders' => 0,
                'total_activity' => 0
            ];
        }
    }

    /**
     * Update last summary timestamp
     */
    private function updateLastSummaryTimestamp($userId, $type) {
        try {
            $pdo = getDbConnection();

            $column = $type === 'daily' ? 'last_daily_summary' : 'last_weekly_summary';

            $stmt = $pdo->prepare("
                UPDATE user_notification_preferences
                SET $column = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE user_id = ?
            ");

            $stmt->execute([$userId]);

        } catch (Exception $e) {
            error_log("Error updating last summary timestamp: " . $e->getMessage());
        }
    }

    /**
     * Get status text in Czech
     */
    private function getStatusText($status) {
        $statusTexts = [
            'not_created' => 'Nevytvořen',
            'in_progress' => 'Probíhá',
            'sent_to_client' => 'Odesláno klientovi',
            'approved' => 'Schváleno',
            'rejected' => 'Zamítnuto',
            'revision_needed' => 'Potřebuje úpravu'
        ];

        return $statusTexts[$status] ?? $status;
    }

    /**
     * Compile email template with data
     */
    private function compileTemplate($templateName, $data) {
        try {
            $templateDir = getEmailTemplateDir();
            $templateFile = $templateDir . $templateName . '.php';

            if (!file_exists($templateFile)) {
                throw new Exception("Template file not found: $templateFile");
            }

            // Extract data for template
            extract($data);

            // Capture template output
            ob_start();
            include $templateFile;
            $htmlContent = ob_get_clean();

            // Generate plain text version
            $textContent = $this->htmlToText($htmlContent);

            return [
                'html' => $htmlContent,
                'text' => $textContent
            ];

        } catch (Exception $e) {
            error_log("Error compiling template: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert HTML to plain text
     */
    private function htmlToText($html) {
        // Remove HTML tags and decode entities
        $text = strip_tags($html);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');

        // Clean up whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Send email via SMTP
     */
    private function sendSMTPEmail($to, $toName, $subject, $htmlBody, $textBody) {
        try {
            $config = getSMTPConfig();

            // In test mode, don't actually send
            if ($this->config['test_mode']) {
                error_log("TEST MODE: Email would be sent to $to with subject: $subject");
                return true;
            }

            // Here you would integrate with PHPMailer or similar SMTP library
            // For now, we'll use PHP's mail() function as a placeholder

            $headers = [
                'MIME-Version: 1.0',
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $config['from_name'] . ' <' . $config['from_email'] . '>',
                'Reply-To: ' . $config['reply_to_name'] . ' <' . $config['reply_to_email'] . '>',
                'X-Mailer: CIG Realizace Email System'
            ];

            $success = mail($to, $subject, $htmlBody, implode("\r\n", $headers));

            if ($this->config['log_all_emails']) {
                error_log("Email sent to $to: " . ($success ? 'SUCCESS' : 'FAILED'));
            }

            return $success;

        } catch (Exception $e) {
            error_log("SMTP send error: " . $e->getMessage());
            return false;
        }
    }
}
