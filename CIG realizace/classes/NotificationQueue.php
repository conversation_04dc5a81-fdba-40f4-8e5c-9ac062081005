<?php
/**
 * NotificationQueue Class - Manage email queue for batch processing
 * CIG Realizace - Phase 08
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/email.php';

class NotificationQueue {
    
    private $config;
    
    public function __construct() {
        $this->config = getQueueConfig();
    }
    
    /**
     * Add email to queue
     */
    public function queueEmail($recipientEmail, $recipientName, $subject, $template, $templateData, $priority = 5) {
        try {
            $pdo = getDbConnection();
            
            // Check rate limiting
            if (!$this->checkRateLimit($recipientEmail)) {
                error_log("Rate limit exceeded for email: $recipientEmail");
                return false;
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO notification_queue (
                    recipient_email, recipient_name, subject, template, 
                    template_data, priority, scheduled_at, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $recipientEmail,
                $recipientName,
                $subject,
                $template,
                json_encode($templateData),
                $priority,
                date('Y-m-d H:i:s'),
                date('Y-m-d H:i:s')
            ]);
            
            if ($result) {
                $queueId = $pdo->lastInsertId();
                
                // Create delivery tracking record
                $this->createDeliveryTracking($queueId, $recipientEmail);
                
                return $queueId;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("NotificationQueue queueEmail error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Process pending emails in queue
     */
    public function processPendingEmails($batchSize = null) {
        try {
            $batchSize = $batchSize ?? $this->config['batch_size'];
            
            // Get pending emails ordered by priority and scheduled time
            $emails = $this->getPendingEmails($batchSize);
            
            if (empty($emails)) {
                return 0;
            }
            
            $processed = 0;
            foreach ($emails as $email) {
                try {
                    // Mark as processing
                    $this->updateEmailStatus($email['id'], 'processing');
                    
                    // Send email
                    $success = $this->sendQueuedEmail($email);
                    
                    if ($success) {
                        $this->markAsSent($email['id']);
                        $processed++;
                    } else {
                        $this->handleFailure($email['id'], 'Failed to send email');
                    }
                    
                } catch (Exception $e) {
                    $this->handleFailure($email['id'], $e->getMessage());
                }
                
                // Small delay to prevent overwhelming SMTP server
                usleep(100000); // 0.1 second
            }
            
            return $processed;
            
        } catch (Exception $e) {
            error_log("NotificationQueue processPendingEmails error: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get pending emails from queue
     */
    public function getPendingEmails($limit = 50) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                SELECT * FROM notification_queue 
                WHERE status = 'pending' 
                AND scheduled_at <= CURRENT_TIMESTAMP
                AND attempts < max_attempts
                ORDER BY priority ASC, scheduled_at ASC 
                LIMIT ?
            ");
            
            $stmt->execute([$limit]);
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting pending emails: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Send queued email
     */
    private function sendQueuedEmail($email) {
        try {
            // Compile template
            $templateData = json_decode($email['template_data'], true);
            $emailContent = $this->compileTemplate($email['template'], $templateData);
            
            if (!$emailContent) {
                throw new Exception("Failed to compile template: " . $email['template']);
            }
            
            // Add tracking pixel if enabled
            if (getEmailConfig()['tracking_pixel_enabled']) {
                $trackingPixel = $this->getTrackingPixel($email['id']);
                $emailContent['html'] = str_replace('</body>', $trackingPixel . '</body>', $emailContent['html']);
            }
            
            // Add unsubscribe link
            $unsubscribeLink = $this->getUnsubscribeLink($email['recipient_email']);
            $emailContent['html'] = str_replace('{{unsubscribe_url}}', $unsubscribeLink, $emailContent['html']);
            $emailContent['text'] = str_replace('{{unsubscribe_url}}', $unsubscribeLink, $emailContent['text']);
            
            // Send email
            return $this->sendSMTPEmail(
                $email['recipient_email'],
                $email['recipient_name'],
                $email['subject'],
                $emailContent['html'],
                $emailContent['text']
            );
            
        } catch (Exception $e) {
            error_log("Error sending queued email: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark email as sent
     */
    public function markAsSent($emailId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                UPDATE notification_queue 
                SET status = 'sent', sent_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([$emailId]);
            
            // Update delivery tracking
            $this->updateDeliveryTracking($emailId, 'delivered');
            
            // Update statistics
            $this->updateEmailStatistics($emailId, 'sent');
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error marking email as sent: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Handle email sending failure
     */
    public function handleFailure($emailId, $errorMessage) {
        try {
            $pdo = getDbConnection();
            
            // Increment attempts
            $stmt = $pdo->prepare("
                UPDATE notification_queue 
                SET attempts = attempts + 1, 
                    error_message = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([$errorMessage, $emailId]);
            
            // Check if max attempts reached
            $stmt = $pdo->prepare("SELECT attempts, max_attempts FROM notification_queue WHERE id = ?");
            $stmt->execute([$emailId]);
            $email = $stmt->fetch();
            
            if ($email && $email['attempts'] >= $email['max_attempts']) {
                $this->markAsFailed($emailId);
            } else {
                // Schedule retry with exponential backoff
                $this->scheduleRetry($emailId, $email['attempts']);
            }
            
        } catch (Exception $e) {
            error_log("Error handling email failure: " . $e->getMessage());
        }
    }
    
    /**
     * Mark email as failed
     */
    public function markAsFailed($emailId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                UPDATE notification_queue 
                SET status = 'failed', updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([$emailId]);
            
            // Update statistics
            $this->updateEmailStatistics($emailId, 'failed');
            
        } catch (Exception $e) {
            error_log("Error marking email as failed: " . $e->getMessage());
        }
    }
    
    /**
     * Schedule email retry with exponential backoff
     */
    private function scheduleRetry($emailId, $attempts) {
        try {
            $pdo = getDbConnection();
            
            // Calculate retry delay: base_delay * (2 ^ attempts)
            $baseDelay = $this->config['retry_delay']; // 5 minutes
            $retryDelay = $baseDelay * pow(2, $attempts - 1);
            
            // Cap at 24 hours
            $retryDelay = min($retryDelay, 86400);
            
            $retryTime = date('Y-m-d H:i:s', time() + $retryDelay);
            
            $stmt = $pdo->prepare("
                UPDATE notification_queue 
                SET scheduled_at = ?, status = 'pending', updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([$retryTime, $emailId]);
            
        } catch (Exception $e) {
            error_log("Error scheduling retry: " . $e->getMessage());
        }
    }
    
    /**
     * Update email status
     */
    private function updateEmailStatus($emailId, $status) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                UPDATE notification_queue 
                SET status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            
            $stmt->execute([$status, $emailId]);
            
        } catch (Exception $e) {
            error_log("Error updating email status: " . $e->getMessage());
        }
    }
    
    /**
     * Check rate limiting for recipient
     */
    private function checkRateLimit($recipientEmail) {
        try {
            $config = getEmailConfig();

            if (!$config['rate_limit_enabled']) {
                return true;
            }

            $pdo = getDbConnection();

            // Check hourly limit
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM notification_queue
                WHERE recipient_email = ?
                AND created_at > datetime('now', '-1 hour')
            ");

            $stmt->execute([$recipientEmail]);
            $hourlyCount = $stmt->fetch()['count'];

            if ($hourlyCount >= $config['rate_limit_per_hour']) {
                return false;
            }

            // Check daily limit
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM notification_queue
                WHERE recipient_email = ?
                AND DATE(created_at) = DATE('now')
            ");

            $stmt->execute([$recipientEmail]);
            $dailyCount = $stmt->fetch()['count'];

            return $dailyCount < $config['rate_limit_per_day'];

        } catch (Exception $e) {
            error_log("Error checking rate limit: " . $e->getMessage());
            return true; // Allow if can't check
        }
    }

    /**
     * Create delivery tracking record
     */
    private function createDeliveryTracking($queueId, $recipientEmail) {
        try {
            $pdo = getDbConnection();

            $stmt = $pdo->prepare("
                INSERT INTO email_delivery_tracking (queue_id, recipient_email, created_at)
                VALUES (?, ?, ?)
            ");

            $stmt->execute([$queueId, $recipientEmail, date('Y-m-d H:i:s')]);

        } catch (Exception $e) {
            error_log("Error creating delivery tracking: " . $e->getMessage());
        }
    }

    /**
     * Update delivery tracking
     */
    private function updateDeliveryTracking($queueId, $status) {
        try {
            $pdo = getDbConnection();

            $column = $status . '_at';

            $stmt = $pdo->prepare("
                UPDATE email_delivery_tracking
                SET $column = CURRENT_TIMESTAMP
                WHERE queue_id = ?
            ");

            $stmt->execute([$queueId]);

        } catch (Exception $e) {
            error_log("Error updating delivery tracking: " . $e->getMessage());
        }
    }

    /**
     * Update email statistics
     */
    private function updateEmailStatistics($queueId, $action) {
        try {
            $pdo = getDbConnection();

            // Get email template name
            $stmt = $pdo->prepare("SELECT template FROM notification_queue WHERE id = ?");
            $stmt->execute([$queueId]);
            $email = $stmt->fetch();

            if (!$email) {
                return;
            }

            $today = date('Y-m-d');
            $templateName = $email['template'];

            // Insert or update statistics
            $stmt = $pdo->prepare("
                INSERT INTO email_statistics (date, template_name, emails_sent, emails_delivered, emails_failed, created_at, updated_at)
                VALUES (?, ?,
                    CASE WHEN ? = 'sent' THEN 1 ELSE 0 END,
                    CASE WHEN ? = 'sent' THEN 1 ELSE 0 END,
                    CASE WHEN ? = 'failed' THEN 1 ELSE 0 END,
                    CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ON CONFLICT(date, template_name) DO UPDATE SET
                    emails_sent = emails_sent + CASE WHEN ? = 'sent' THEN 1 ELSE 0 END,
                    emails_delivered = emails_delivered + CASE WHEN ? = 'sent' THEN 1 ELSE 0 END,
                    emails_failed = emails_failed + CASE WHEN ? = 'failed' THEN 1 ELSE 0 END,
                    updated_at = CURRENT_TIMESTAMP
            ");

            $stmt->execute([
                $today, $templateName, $action, $action, $action, $action, $action, $action
            ]);

        } catch (Exception $e) {
            error_log("Error updating email statistics: " . $e->getMessage());
        }
    }

    /**
     * Get tracking pixel HTML
     */
    private function getTrackingPixel($queueId) {
        $trackingUrl = getEmailTrackingUrl($queueId);
        return '<img src="' . $trackingUrl . '" width="1" height="1" style="display:none;" alt="">';
    }

    /**
     * Get unsubscribe link
     */
    private function getUnsubscribeLink($recipientEmail) {
        // Get user ID from email
        try {
            $pdo = getDbConnection();
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$recipientEmail]);
            $user = $stmt->fetch();

            if ($user) {
                $token = generateUnsubscribeToken($user['id']);
                return getUnsubscribeUrl($user['id'], $token);
            }

        } catch (Exception $e) {
            error_log("Error getting unsubscribe link: " . $e->getMessage());
        }

        return '#';
    }

    /**
     * Compile email template
     */
    private function compileTemplate($templateName, $data) {
        try {
            $templateDir = getEmailTemplateDir();
            $templateFile = $templateDir . $templateName . '.php';

            if (!file_exists($templateFile)) {
                throw new Exception("Template file not found: $templateFile");
            }

            // Extract data for template
            extract($data);

            // Capture template output
            ob_start();
            include $templateFile;
            $htmlContent = ob_get_clean();

            // Generate plain text version
            $textContent = $this->htmlToText($htmlContent);

            return [
                'html' => $htmlContent,
                'text' => $textContent
            ];

        } catch (Exception $e) {
            error_log("Error compiling template: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert HTML to plain text
     */
    private function htmlToText($html) {
        // Remove HTML tags and decode entities
        $text = strip_tags($html);
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');

        // Clean up whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Send email via SMTP
     */
    private function sendSMTPEmail($to, $toName, $subject, $htmlBody, $textBody) {
        try {
            $config = getSMTPConfig();
            $emailConfig = getEmailConfig();

            // In test mode, don't actually send
            if ($emailConfig['test_mode']) {
                error_log("TEST MODE: Email would be sent to $to with subject: $subject");
                return true;
            }

            // Here you would integrate with PHPMailer or similar SMTP library
            // For now, we'll use PHP's mail() function as a placeholder

            $headers = [
                'MIME-Version: 1.0',
                'Content-Type: text/html; charset=UTF-8',
                'From: ' . $config['from_name'] . ' <' . $config['from_email'] . '>',
                'Reply-To: ' . $config['reply_to_name'] . ' <' . $config['reply_to_email'] . '>',
                'X-Mailer: CIG Realizace Email System'
            ];

            $success = mail($to, $subject, $htmlBody, implode("\r\n", $headers));

            if ($emailConfig['log_all_emails']) {
                error_log("Email sent to $to: " . ($success ? 'SUCCESS' : 'FAILED'));
            }

            return $success;

        } catch (Exception $e) {
            error_log("SMTP send error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get queue statistics
     */
    public function getQueueStatistics() {
        try {
            $pdo = getDbConnection();

            $stmt = $pdo->prepare("
                SELECT
                    status,
                    COUNT(*) as count,
                    MIN(created_at) as oldest,
                    MAX(created_at) as newest
                FROM notification_queue
                GROUP BY status
            ");

            $stmt->execute();
            $stats = $stmt->fetchAll();

            $result = [
                'pending' => 0,
                'sent' => 0,
                'failed' => 0,
                'processing' => 0,
                'total' => 0,
                'oldest_pending' => null,
                'newest_sent' => null
            ];

            foreach ($stats as $stat) {
                $result[$stat['status']] = $stat['count'];
                $result['total'] += $stat['count'];

                if ($stat['status'] === 'pending' && $stat['oldest']) {
                    $result['oldest_pending'] = $stat['oldest'];
                }
                if ($stat['status'] === 'sent' && $stat['newest']) {
                    $result['newest_sent'] = $stat['newest'];
                }
            }

            return $result;

        } catch (Exception $e) {
            error_log("Error getting queue statistics: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Clean old processed emails from queue
     */
    public function cleanOldEmails($daysOld = 30) {
        try {
            $pdo = getDbConnection();

            $stmt = $pdo->prepare("
                DELETE FROM notification_queue
                WHERE status IN ('sent', 'failed')
                AND created_at < datetime('now', '-' || ? || ' days')
            ");

            $stmt->execute([$daysOld]);

            return $stmt->rowCount();

        } catch (Exception $e) {
            error_log("Error cleaning old emails: " . $e->getMessage());
            return 0;
        }
    }
}
