<?php
/**
 * User Management Class
 * Handles user operations like creation, authentication, and management
 */
class User {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Create a new user
     */
    public function createUser($username, $password, $full_name, $role, $email = null) {
        try {
            // Check if username already exists
            $stmt = $this->pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                throw new Exception('Uživatelské jméno již existuje.');
            }
            
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $stmt = $this->pdo->prepare("
                INSERT INTO users (username, password, full_name, role, email, is_active, created_at) 
                VALUES (?, ?, ?, ?, ?, 1, datetime('now'))
            ");
            
            $stmt->execute([$username, $hashed_password, $full_name, $role, $email]);
            
            return $this->pdo->lastInsertId();
            
        } catch (PDOException $e) {
            throw new Exception('<PERSON>y<PERSON> při vytváření uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Get user by ID
     */
    public function getUserById($id) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$id]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('Chyba při načítání uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Get user by username
     */
    public function getUserByUsername($username) {
        try {
            $stmt = $this->pdo->prepare("SELECT * FROM users WHERE username = ?");
            $stmt->execute([$username]);
            return $stmt->fetch();
        } catch (PDOException $e) {
            throw new Exception('Chyba při načítání uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Get all users
     */
    public function getAllUsers($include_inactive = false) {
        try {
            $sql = "SELECT * FROM users";
            if (!$include_inactive) {
                $sql .= " WHERE is_active = 1";
            }
            $sql .= " ORDER BY full_name";
            
            $stmt = $this->pdo->query($sql);
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            throw new Exception('Chyba při načítání uživatelů: ' . $e->getMessage());
        }
    }
    
    /**
     * Update user
     */
    public function updateUser($id, $username, $full_name, $role, $email = null, $password = null) {
        try {
            // Check if username is taken by another user
            $stmt = $this->pdo->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$username, $id]);
            if ($stmt->fetch()) {
                throw new Exception('Uživatelské jméno již používá jiný uživatel.');
            }
            
            // Prepare update query
            if ($password) {
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $this->pdo->prepare("
                    UPDATE users 
                    SET username = ?, password = ?, full_name = ?, role = ?, email = ?, updated_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$username, $hashed_password, $full_name, $role, $email, $id]);
            } else {
                $stmt = $this->pdo->prepare("
                    UPDATE users 
                    SET username = ?, full_name = ?, role = ?, email = ?, updated_at = datetime('now')
                    WHERE id = ?
                ");
                $stmt->execute([$username, $full_name, $role, $email, $id]);
            }
            
            return true;
            
        } catch (PDOException $e) {
            throw new Exception('Chyba při aktualizaci uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete user (soft delete - set inactive)
     */
    public function deleteUser($id) {
        try {
            // Don't allow deleting the admin user
            $user = $this->getUserById($id);
            if ($user && $user['username'] === 'admin') {
                throw new Exception('Administrátorský účet nelze smazat.');
            }
            
            $stmt = $this->pdo->prepare("UPDATE users SET is_active = 0, updated_at = datetime('now') WHERE id = ?");
            $stmt->execute([$id]);
            
            return true;
            
        } catch (PDOException $e) {
            throw new Exception('Chyba při mazání uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Activate/Deactivate user
     */
    public function toggleUserStatus($id) {
        try {
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END,
                    updated_at = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([$id]);
            
            return true;
            
        } catch (PDOException $e) {
            throw new Exception('Chyba při změně stavu uživatele: ' . $e->getMessage());
        }
    }
    
    /**
     * Verify user password
     */
    public function verifyPassword($username, $password) {
        try {
            $user = $this->getUserByUsername($username);
            if ($user && $user['is_active'] && password_verify($password, $user['password'])) {
                return $user;
            }
            return false;
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * Get available roles
     */
    public function getAvailableRoles() {
        return [
            'admin' => 'Administrátor',
            'obchodnik' => 'Obchodník',
            'grafik' => 'Grafik',
            'realizator' => 'Realizátor'
        ];
    }
    
    /**
     * Get role display name
     */
    public function getRoleDisplayName($role) {
        $roles = $this->getAvailableRoles();
        return $roles[$role] ?? $role;
    }
    
    /**
     * Change user password
     */
    public function changePassword($id, $new_password) {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("
                UPDATE users 
                SET password = ?, updated_at = datetime('now')
                WHERE id = ?
            ");
            $stmt->execute([$hashed_password, $id]);
            
            return true;
            
        } catch (PDOException $e) {
            throw new Exception('Chyba při změně hesla: ' . $e->getMessage());
        }
    }
}
?>
