<?php
/**
 * CSV Importer Class
 * CIG Realizace - Order Management System
 * 
 * Handles CSV file import with proper encoding detection and data processing
 */

class CSVImporter {
    private $pdo;
    private $errors = [];
    private $warnings = [];
    private $stats = [
        'total_rows' => 0,
        'processed_rows' => 0,
        'orders_created' => 0,
        'items_created' => 0,
        'errors' => 0,
        'warnings' => 0
    ];
    
    // Sales representative mapping based on order prefix
    private $sales_rep_mapping = [
        'VP' => 'Vláďa',
        'J' => 'Jirka',
        'NK' => 'Nikol',
        'MR' => 'Mirka',
        'D' => 'Daniela',
        'CI' => 'Czech Image'
    ];
    
    // CSV column mapping
    private $csv_columns = [
        'order_code' => 'Číslo dokladu',
        'catalog' => 'Katalog',
        'quantity' => 'Množ. hlav. jedn.',
        'order_date' => 'Datum vystavení',
        'goods_ordered_date' => 'Datum vystavení OV',
        'goods_stocked_date' => 'Datum vytvoření  DLP'
    ];
    
    // Items to ignore during import
    private $ignore_catalogs = ['Poštovné', 'Dopravné', 'PVY'];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Import CSV file
     * @param string $file_path Path to CSV file
     * @param bool $preview_only If true, only validate and preview data
     * @return array Import results
     */
    public function importCSV($file_path, $preview_only = false) {
        $this->resetStats();
        
        try {
            // Read and convert file encoding
            $csv_content = $this->readAndConvertFile($file_path);
            if (!$csv_content) {
                throw new Exception('Nepodařilo se načíst CSV soubor');
            }
            
            // Parse CSV data
            $csv_data = $this->parseCSV($csv_content);
            if (empty($csv_data)) {
                throw new Exception('CSV soubor neobsahuje platná data');
            }
            
            // Validate CSV structure
            $this->validateCSVStructure($csv_data);
            
            // Group data by order code
            $grouped_orders = $this->groupOrdersByCode($csv_data);
            
            if (!$preview_only) {
                // Process orders and insert into database
                $this->processOrders($grouped_orders);
            }
            
            return [
                'success' => true,
                'stats' => $this->stats,
                'errors' => $this->errors,
                'warnings' => $this->warnings,
                'preview_data' => $preview_only ? array_slice($grouped_orders, 0, 5) : null
            ];
            
        } catch (Exception $e) {
            $this->errors[] = $e->getMessage();
            return [
                'success' => false,
                'stats' => $this->stats,
                'errors' => $this->errors,
                'warnings' => $this->warnings
            ];
        }
    }
    
    /**
     * Read file and convert encoding from Windows-1250 to UTF-8
     */
    private function readAndConvertFile($file_path) {
        if (!file_exists($file_path)) {
            throw new Exception('Soubor nebyl nalezen: ' . $file_path);
        }
        
        $content = file_get_contents($file_path);
        if ($content === false) {
            throw new Exception('Nepodařilo se načíst obsah souboru');
        }
        
        // Convert from Windows-1250 to UTF-8
        $test_convert = @iconv('Windows-1250', 'UTF-8//IGNORE', $content);
        if ($test_convert !== false && strlen($test_convert) > 0) {
            $content = $test_convert;
        } else {
            // Fallback: try to detect encoding
            $encoding = mb_detect_encoding($content, ['UTF-8', 'ISO-8859-2'], true);
            if ($encoding && $encoding !== 'UTF-8') {
                $content = mb_convert_encoding($content, 'UTF-8', $encoding);
            }
        }
        
        return $content;
    }
    
    /**
     * Parse CSV content into array
     */
    private function parseCSV($content) {
        $lines = explode("\n", $content);
        $data = [];
        $header = null;
        
        foreach ($lines as $line_num => $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            // Parse CSV line (semicolon separated)
            $fields = str_getcsv($line, ';', '"', '\\');
            
            if ($header === null) {
                // First line is header
                $header = $fields;
                continue;
            }
            
            // Create associative array
            $row = [];
            foreach ($header as $index => $column_name) {
                $row[$column_name] = isset($fields[$index]) ? trim($fields[$index]) : '';
            }
            
            $row['_line_number'] = $line_num + 1;
            $data[] = $row;
            $this->stats['total_rows']++;
        }
        
        return $data;
    }
    
    /**
     * Validate CSV structure
     */
    private function validateCSVStructure($data) {
        if (empty($data)) {
            throw new Exception('CSV soubor je prázdný');
        }

        $first_row = $data[0];
        $required_columns = array_values($this->csv_columns);
        $available_columns = array_keys($first_row);

        // Create normalized mapping for comparison
        $normalized_available = [];
        foreach ($available_columns as $col) {
            $normalized_available[trim(strtolower($col))] = $col;
        }

        foreach ($required_columns as $column) {
            $normalized_required = trim(strtolower($column));

            if (!array_key_exists($column, $first_row) && !array_key_exists($normalized_required, $normalized_available)) {
                throw new Exception("Chybí povinný sloupec: '$column'. Dostupné sloupce: " . implode(', ', $available_columns));
            }
        }
    }
    
    /**
     * Group CSV rows by order code
     */
    private function groupOrdersByCode($data) {
        $grouped = [];
        
        foreach ($data as $row) {
            $order_code = $row[$this->csv_columns['order_code']];
            $catalog = $row[$this->csv_columns['catalog']];
            
            // Skip empty order codes
            if (empty($order_code)) {
                $this->warnings[] = "Řádek {$row['_line_number']}: Prázdné číslo dokladu";
                continue;
            }
            
            // Skip ignored catalog items
            if (in_array($catalog, $this->ignore_catalogs)) {
                continue;
            }
            
            // Skip empty catalog and zero quantity
            $quantity = $this->parseQuantity($row[$this->csv_columns['quantity']]);
            if (empty($catalog) && $quantity <= 0) {
                continue;
            }
            
            if (!isset($grouped[$order_code])) {
                $grouped[$order_code] = [
                    'order_code' => $order_code,
                    'order_date' => $this->parseDate($row[$this->csv_columns['order_date']]),
                    'sales_rep' => $this->extractSalesRep($order_code),
                    'items' => []
                ];
            }
            
            // Add item if it has catalog or quantity
            if (!empty($catalog) || $quantity > 0) {
                $grouped[$order_code]['items'][] = [
                    'catalog' => $catalog,
                    'quantity' => $quantity,
                    'goods_ordered_date' => $this->parseDate($row[$this->csv_columns['goods_ordered_date']]),
                    'goods_stocked_date' => $this->parseDate($row[$this->csv_columns['goods_stocked_date']]),
                    'inventory_status' => $this->determineInventoryStatus($row),
                    'line_number' => $row['_line_number']
                ];
            }
        }
        
        return $grouped;
    }
    
    /**
     * Extract sales representative from order code prefix
     */
    private function extractSalesRep($order_code) {
        // Extract prefix (e.g., "25VP-00001" -> "VP")
        if (preg_match('/^\d*([A-Z]+)-/', $order_code, $matches)) {
            $prefix = $matches[1];
            return $this->sales_rep_mapping[$prefix] ?? 'Neznámý';
        }
        return 'Neznámý';
    }

    /**
     * Extract sales representative prefix from order code
     */
    private function extractSalesRepPrefix($order_code) {
        // Extract prefix (e.g., "25VP-00001" -> "VP")
        if (preg_match('/^\d*([A-Z]+)-/', $order_code, $matches)) {
            return $matches[1];
        }
        return 'XX';
    }
    
    /**
     * Parse quantity from string (handle Czech decimal format)
     */
    private function parseQuantity($quantity_str) {
        if (empty($quantity_str)) return 0;
        
        // Convert Czech decimal format (comma) to dot
        $quantity_str = str_replace(',', '.', $quantity_str);
        return (float) $quantity_str;
    }
    
    /**
     * Parse date from Czech format (DD.MM.YYYY)
     */
    private function parseDate($date_str) {
        if (empty($date_str)) return null;
        
        if (preg_match('/^(\d{2})\.(\d{2})\.(\d{4})$/', $date_str, $matches)) {
            return $matches[3] . '-' . $matches[2] . '-' . $matches[1];
        }
        
        return null;
    }
    
    /**
     * Determine inventory status based on dates
     */
    private function determineInventoryStatus($row) {
        $goods_stocked_date = $row[$this->csv_columns['goods_stocked_date']];
        $goods_ordered_date = $row[$this->csv_columns['goods_ordered_date']];
        
        if (!empty($goods_stocked_date)) {
            return 'in_stock';
        } elseif (!empty($goods_ordered_date)) {
            return 'ordered';
        } else {
            return 'not_in_stock';
        }
    }
    
    /**
     * Reset statistics
     */
    private function resetStats() {
        $this->stats = [
            'total_rows' => 0,
            'processed_rows' => 0,
            'orders_created' => 0,
            'items_created' => 0,
            'errors' => 0,
            'warnings' => 0
        ];
        $this->errors = [];
        $this->warnings = [];
    }
    
    /**
     * Get import statistics
     */
    public function getStats() {
        return $this->stats;
    }
    
    /**
     * Get errors
     */
    public function getErrors() {
        return $this->errors;
    }
    
    /**
     * Get warnings
     */
    public function getWarnings() {
        return $this->warnings;
    }

    /**
     * Process orders and insert into database
     */
    private function processOrders($grouped_orders) {
        $this->pdo->beginTransaction();

        try {
            foreach ($grouped_orders as $order_data) {
                $this->processOrder($order_data);
            }

            $this->pdo->commit();

        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }

    /**
     * Process single order
     */
    private function processOrder($order_data) {
        // Check if order already exists
        $stmt = $this->pdo->prepare("SELECT id FROM orders WHERE order_code = ?");
        $stmt->execute([$order_data['order_code']]);

        if ($stmt->fetch()) {
            $this->warnings[] = "Objednávka {$order_data['order_code']} již existuje - přeskočena";
            return;
        }

        // Get sales rep user ID
        $sales_rep_id = $this->getSalesRepId($order_data['sales_rep']);

        // Extract sales rep prefix
        $sales_rep_prefix = $this->extractSalesRepPrefix($order_data['order_code']);

        // Insert order
        $stmt = $this->pdo->prepare("
            INSERT INTO orders (order_code, order_date, sales_rep_id, sales_rep, sales_rep_name, status, created_at)
            VALUES (?, ?, ?, ?, ?, 'pending', datetime('now'))
        ");

        $stmt->execute([
            $order_data['order_code'],
            $order_data['order_date'],
            $sales_rep_id,
            $sales_rep_prefix,
            $order_data['sales_rep']
        ]);

        $order_id = $this->pdo->lastInsertId();
        $this->stats['orders_created']++;

        // Insert order items
        foreach ($order_data['items'] as $item) {
            $this->processOrderItem($order_id, $item);
        }

        $this->stats['processed_rows']++;
    }

    /**
     * Process single order item
     */
    private function processOrderItem($order_id, $item) {
        $stmt = $this->pdo->prepare("
            INSERT INTO order_items (
                order_id, catalog_code, quantity,
                goods_ordered_date, goods_stocked_date, inventory_status,
                created_at
            ) VALUES (?, ?, ?, ?, ?, ?, datetime('now'))
        ");

        $stmt->execute([
            $order_id,
            $item['catalog'],
            $item['quantity'],
            $item['goods_ordered_date'],
            $item['goods_stocked_date'],
            $item['inventory_status']
        ]);

        $this->stats['items_created']++;
    }

    /**
     * Get sales representative user ID
     */
    private function getSalesRepId($sales_rep_name) {
        $stmt = $this->pdo->prepare("SELECT id FROM users WHERE full_name = ? AND role = 'obchodnik'");
        $stmt->execute([$sales_rep_name]);
        $user = $stmt->fetch();

        return $user ? $user['id'] : null;
    }
}
