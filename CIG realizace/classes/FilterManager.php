<?php
/**
 * FilterManager Class
 * CIG Realizace - Phase 09
 * Advanced filter management and saved filters functionality
 */

class FilterManager {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDbConnection();
    }
    
    /**
     * Build advanced query with filters
     */
    public function buildAdvancedQuery($baseQuery, $filters, $params = []) {
        $conditions = [];
        $newParams = $params;
        
        // Sales representative filter
        if (!empty($filters['sales_rep'])) {
            if (is_array($filters['sales_rep'])) {
                $placeholders = str_repeat('?,', count($filters['sales_rep']) - 1) . '?';
                $conditions[] = "o.sales_rep IN ($placeholders)";
                $newParams = array_merge($newParams, $filters['sales_rep']);
            } else {
                $conditions[] = "o.sales_rep = ?";
                $newParams[] = $filters['sales_rep'];
            }
        }
        
        // Preview status filter
        if (!empty($filters['preview_status'])) {
            if (is_array($filters['preview_status'])) {
                $placeholders = str_repeat('?,', count($filters['preview_status']) - 1) . '?';
                $conditions[] = "o.preview_status IN ($placeholders)";
                $newParams = array_merge($newParams, $filters['preview_status']);
            } else {
                $conditions[] = "o.preview_status = ?";
                $newParams[] = $filters['preview_status'];
            }
        }
        
        // Order status filter
        if (!empty($filters['status'])) {
            if (is_array($filters['status'])) {
                $placeholders = str_repeat('?,', count($filters['status']) - 1) . '?';
                $conditions[] = "o.status IN ($placeholders)";
                $newParams = array_merge($newParams, $filters['status']);
            } else {
                $conditions[] = "o.status = ?";
                $newParams[] = $filters['status'];
            }
        }
        
        // Date range filters
        if (!empty($filters['date_from'])) {
            $date_from = $this->processDateFilter($filters['date_from']);
            $conditions[] = "o.order_date >= ?";
            $newParams[] = $date_from;
        }
        
        if (!empty($filters['date_to'])) {
            $date_to = $this->processDateFilter($filters['date_to']);
            $conditions[] = "o.order_date <= ?";
            $newParams[] = $date_to;
        }
        
        // Technology filter
        if (!empty($filters['technology'])) {
            $conditions[] = "EXISTS (
                SELECT 1 FROM order_items oi 
                WHERE oi.order_id = o.id 
                  AND oi.is_relevant = 1 
                  AND oi.technology_assignment LIKE ?
            )";
            $newParams[] = '%' . $filters['technology'] . '%';
        }
        
        // Inventory status filter
        if (!empty($filters['inventory_status'])) {
            if (is_array($filters['inventory_status'])) {
                $placeholders = str_repeat('?,', count($filters['inventory_status']) - 1) . '?';
                $conditions[] = "EXISTS (
                    SELECT 1 FROM order_items oi 
                    WHERE oi.order_id = o.id 
                      AND oi.is_relevant = 1 
                      AND oi.inventory_status IN ($placeholders)
                )";
                $newParams = array_merge($newParams, $filters['inventory_status']);
            } else {
                $conditions[] = "EXISTS (
                    SELECT 1 FROM order_items oi 
                    WHERE oi.order_id = o.id 
                      AND oi.is_relevant = 1 
                      AND oi.inventory_status = ?
                )";
                $newParams[] = $filters['inventory_status'];
            }
        }
        
        // Completion status filter
        if (isset($filters['is_completed'])) {
            $conditions[] = "o.is_completed = ?";
            $newParams[] = $filters['is_completed'] ? 1 : 0;
        }
        
        // Overdue filter
        if (!empty($filters['overdue_only'])) {
            $conditions[] = "o.expected_delivery_date < CURDATE() AND o.is_completed = 0";
        }
        
        // Search filter
        if (!empty($filters['search'])) {
            $search_term = '%' . $filters['search'] . '%';
            $conditions[] = "(
                o.order_code LIKE ? OR 
                o.sales_rep_name LIKE ? OR 
                o.customer_name LIKE ? OR
                EXISTS (
                    SELECT 1 FROM order_items oi 
                    WHERE oi.order_id = o.id 
                      AND oi.is_relevant = 1 
                      AND (oi.catalog_code LIKE ? OR oi.technology_assignment LIKE ?)
                )
            )";
            $newParams = array_merge($newParams, [$search_term, $search_term, $search_term, $search_term, $search_term]);
        }
        
        // Build final query
        if (!empty($conditions)) {
            $baseQuery .= (strpos($baseQuery, 'WHERE') !== false ? ' AND ' : ' WHERE ') . 
                         implode(' AND ', $conditions);
        }
        
        return ['query' => $baseQuery, 'params' => $newParams];
    }
    
    /**
     * Save user filter
     */
    public function saveFilter($userId, $name, $filters, $isPublic = false) {
        try {
            $sql = "INSERT INTO saved_filters (user_id, name, filters, is_public) 
                    VALUES (?, ?, ?, ?)
                    ON DUPLICATE KEY UPDATE 
                        filters = VALUES(filters), 
                        is_public = VALUES(is_public),
                        updated_at = CURRENT_TIMESTAMP";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $userId, 
                $name, 
                json_encode($filters), 
                $isPublic ? 1 : 0
            ]);
            
            return $result;
        } catch (Exception $e) {
            error_log("Error saving filter: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Load user filter
     */
    public function loadFilter($userId, $filterId) {
        try {
            $sql = "SELECT * FROM saved_filters 
                    WHERE id = ? AND (user_id = ? OR is_public = 1)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$filterId, $userId]);
            
            $filter = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($filter) {
                $filter['filters'] = json_decode($filter['filters'], true);
            }
            
            return $filter;
        } catch (Exception $e) {
            error_log("Error loading filter: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user's saved filters
     */
    public function getUserFilters($userId) {
        try {
            $sql = "SELECT id, name, is_public, created_at, updated_at 
                    FROM saved_filters 
                    WHERE user_id = ? OR is_public = 1
                    ORDER BY is_public ASC, name ASC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$userId]);
            
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting user filters: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Delete user filter
     */
    public function deleteFilter($userId, $filterId) {
        try {
            $sql = "DELETE FROM saved_filters 
                    WHERE id = ? AND user_id = ?";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute([$filterId, $userId]);
        } catch (Exception $e) {
            error_log("Error deleting filter: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get filter presets
     */
    public function getFilterPresets() {
        try {
            $sql = "SELECT * FROM filter_presets 
                    ORDER BY sort_order ASC, name ASC";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute();
            
            $presets = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Process dynamic date values
            foreach ($presets as &$preset) {
                $preset['filters'] = json_decode($preset['filters'], true);
                $preset['filters'] = $this->processDynamicFilters($preset['filters']);
            }
            
            return $presets;
        } catch (Exception $e) {
            error_log("Error getting filter presets: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Process dynamic filter values (like "today", "week_start", etc.)
     */
    private function processDynamicFilters($filters) {
        if (isset($filters['date_from'])) {
            $filters['date_from'] = $this->processDateFilter($filters['date_from']);
        }
        
        if (isset($filters['date_to'])) {
            $filters['date_to'] = $this->processDateFilter($filters['date_to']);
        }
        
        return $filters;
    }
    
    /**
     * Process date filter values
     */
    private function processDateFilter($dateValue) {
        switch ($dateValue) {
            case 'today':
                return date('Y-m-d');
            case 'yesterday':
                return date('Y-m-d', strtotime('-1 day'));
            case 'week_start':
                return date('Y-m-d', strtotime('monday this week'));
            case 'week_end':
                return date('Y-m-d', strtotime('sunday this week'));
            case 'month_start':
                return date('Y-m-01');
            case 'month_end':
                return date('Y-m-t');
            default:
                return $dateValue;
        }
    }
    
    /**
     * Export filtered data to CSV
     */
    public function exportFilteredData($filters, $format = 'csv') {
        $baseQuery = "
            SELECT o.order_code, o.sales_rep_name, o.order_date, 
                   o.preview_status, o.status, o.is_completed,
                   o.expected_delivery_date, o.customer_name,
                   oi.catalog_code, oi.quantity, oi.technology_assignment,
                   oi.inventory_status, oi.goods_ordered_date, oi.goods_stocked_date
            FROM orders o
            LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
        ";
        
        $result = $this->buildAdvancedQuery($baseQuery, $filters);
        $result['query'] .= " ORDER BY o.order_date DESC, o.order_code ASC";
        
        $stmt = $this->pdo->prepare($result['query']);
        $stmt->execute($result['params']);
        $data = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        switch ($format) {
            case 'csv':
                return $this->exportToCSV($data);
            case 'excel':
                return $this->exportToExcel($data);
            default:
                return false;
        }
    }
    
    /**
     * Export data to CSV
     */
    private function exportToCSV($data) {
        if (empty($data)) {
            return false;
        }
        
        $filename = 'filtered_orders_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        // Add BOM for Excel UTF-8 support
        echo "\xEF\xBB\xBF";
        
        $output = fopen('php://output', 'w');
        
        // Headers in Czech
        $headers = [
            'Kód objednávky', 'Obchodník', 'Datum objednávky', 
            'Stav náhledu', 'Stav objednávky', 'Dokončeno',
            'Termín dodání', 'Zákazník', 'Katalog', 'Množství', 
            'Technologie', 'Stav zásob', 'Datum objednání', 'Datum naskladnění'
        ];
        
        fputcsv($output, $headers, ';');
        
        foreach ($data as $row) {
            fputcsv($output, $row, ';');
        }
        
        fclose($output);
        return true;
    }
    
    /**
     * Export data to Excel (HTML table)
     */
    private function exportToExcel($data) {
        if (empty($data)) {
            return false;
        }
        
        $filename = 'filtered_orders_' . date('Y-m-d_H-i-s') . '.xls';
        
        header('Content-Type: application/vnd.ms-excel; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');
        
        echo '<html><head><meta charset="utf-8"></head><body>';
        echo '<table border="1">';
        
        // Headers
        echo '<tr>';
        $headers = [
            'Kód objednávky', 'Obchodník', 'Datum objednávky', 
            'Stav náhledu', 'Stav objednávky', 'Dokončeno',
            'Termín dodání', 'Zákazník', 'Katalog', 'Množství', 
            'Technologie', 'Stav zásob', 'Datum objednání', 'Datum naskladnění'
        ];
        
        foreach ($headers as $header) {
            echo '<th>' . htmlspecialchars($header) . '</th>';
        }
        echo '</tr>';
        
        // Data
        foreach ($data as $row) {
            echo '<tr>';
            foreach ($row as $cell) {
                echo '<td>' . htmlspecialchars($cell ?? '') . '</td>';
            }
            echo '</tr>';
        }
        
        echo '</table></body></html>';
        return true;
    }
}
