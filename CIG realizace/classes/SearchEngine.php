<?php
/**
 * SearchEngine Class
 * CIG Realizace - Phase 09
 * Advanced search functionality across orders, items, and catalog codes
 */

class SearchEngine {
    private $pdo;
    
    public function __construct() {
        $this->pdo = getDbConnection();
    }
    
    /**
     * Global search across all data types
     */
    public function globalSearch($query, $filters = [], $limit = 50) {
        $start_time = microtime(true);
        
        $results = [
            'orders' => [],
            'items' => [],
            'catalog_codes' => [],
            'technologies' => [],
            'total_results' => 0
        ];
        
        if (strlen(trim($query)) < 2) {
            return $results;
        }
        
        // Search in orders
        $results['orders'] = $this->searchOrders($query, $filters, $limit);
        
        // Search in order items
        $results['items'] = $this->searchOrderItems($query, $filters, $limit);
        
        // Search in catalog codes
        $results['catalog_codes'] = $this->searchCatalogCodes($query, $limit);
        
        // Search in technologies
        $results['technologies'] = $this->searchTechnologies($query, $limit);
        
        // Calculate total results
        $results['total_results'] = count($results['orders']) + 
                                   count($results['items']) + 
                                   count($results['catalog_codes']) + 
                                   count($results['technologies']);
        
        // Log search analytics
        $execution_time = round((microtime(true) - $start_time) * 1000);
        $this->logSearchAnalytics($query, 'global', $results['total_results'], $execution_time);
        
        return $results;
    }
    
    /**
     * Search in orders using full-text search
     */
    private function searchOrders($query, $filters = [], $limit = 25) {
        $search_term = $this->prepareSearchTerm($query);
        
        $sql = "SELECT o.*, 
                       MATCH(o.search_vector) AGAINST(? IN BOOLEAN MODE) as relevance_score,
                       COUNT(oi.id) as items_count
                FROM orders o
                LEFT JOIN order_items oi ON o.id = oi.order_id AND oi.is_relevant = 1
                WHERE MATCH(o.search_vector) AGAINST(? IN BOOLEAN MODE)";
        
        $params = [$search_term, $search_term];
        
        // Apply additional filters
        $filter_conditions = $this->buildFilterConditions($filters, $params);
        if (!empty($filter_conditions)) {
            $sql .= " AND " . implode(" AND ", $filter_conditions);
        }
        
        $sql .= " GROUP BY o.id
                  ORDER BY relevance_score DESC, o.created_at DESC 
                  LIMIT ?";
        
        $params[] = $limit;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search in order items
     */
    private function searchOrderItems($query, $filters = [], $limit = 25) {
        $search_term = $this->prepareSearchTerm($query);
        
        $sql = "SELECT oi.*, o.order_code, o.sales_rep_name,
                       MATCH(oi.search_vector) AGAINST(? IN BOOLEAN MODE) as relevance_score
                FROM order_items oi
                JOIN orders o ON oi.order_id = o.id
                WHERE oi.is_relevant = 1 
                  AND MATCH(oi.search_vector) AGAINST(? IN BOOLEAN MODE)";
        
        $params = [$search_term, $search_term];
        
        // Apply additional filters
        $filter_conditions = $this->buildFilterConditions($filters, $params, 'o');
        if (!empty($filter_conditions)) {
            $sql .= " AND " . implode(" AND ", $filter_conditions);
        }
        
        $sql .= " ORDER BY relevance_score DESC, oi.created_at DESC 
                  LIMIT ?";
        
        $params[] = $limit;
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search in catalog codes
     */
    private function searchCatalogCodes($query, $limit = 15) {
        $sql = "SELECT DISTINCT catalog_code, 
                       COUNT(*) as usage_count,
                       MAX(created_at) as last_used
                FROM order_items 
                WHERE catalog_code LIKE ? 
                  AND is_relevant = 1
                GROUP BY catalog_code
                ORDER BY usage_count DESC, catalog_code ASC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $query . '%', $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search in technologies
     */
    private function searchTechnologies($query, $limit = 15) {
        $sql = "SELECT DISTINCT technology_assignment as technology, 
                       COUNT(*) as usage_count,
                       MAX(oi.created_at) as last_used
                FROM order_items oi
                WHERE technology_assignment LIKE ? 
                  AND technology_assignment IS NOT NULL 
                  AND technology_assignment != ''
                  AND is_relevant = 1
                GROUP BY technology_assignment
                ORDER BY usage_count DESC, technology_assignment ASC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $query . '%', $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get search suggestions for auto-complete
     */
    public function getSearchSuggestions($query, $type = 'all', $limit = 10) {
        $suggestions = [];
        
        if (strlen(trim($query)) < 2) {
            return $suggestions;
        }
        
        switch ($type) {
            case 'orders':
                $suggestions = $this->getOrderSuggestions($query, $limit);
                break;
            case 'catalog':
                $suggestions = $this->getCatalogSuggestions($query, $limit);
                break;
            case 'technology':
                $suggestions = $this->getTechnologySuggestions($query, $limit);
                break;
            default:
                $suggestions = [
                    'orders' => $this->getOrderSuggestions($query, 5),
                    'catalog' => $this->getCatalogSuggestions($query, 5),
                    'technology' => $this->getTechnologySuggestions($query, 5)
                ];
        }
        
        return $suggestions;
    }
    
    /**
     * Get order code suggestions
     */
    private function getOrderSuggestions($query, $limit) {
        $sql = "SELECT order_code, sales_rep_name, id
                FROM orders 
                WHERE order_code LIKE ?
                ORDER BY created_at DESC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $query . '%', $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get catalog code suggestions
     */
    private function getCatalogSuggestions($query, $limit) {
        $sql = "SELECT DISTINCT catalog_code, COUNT(*) as count
                FROM order_items 
                WHERE catalog_code LIKE ? AND is_relevant = 1
                GROUP BY catalog_code
                ORDER BY count DESC, catalog_code ASC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $query . '%', $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get technology suggestions
     */
    private function getTechnologySuggestions($query, $limit) {
        $sql = "SELECT DISTINCT technology_assignment as technology, COUNT(*) as count
                FROM order_items 
                WHERE technology_assignment LIKE ? 
                  AND technology_assignment IS NOT NULL 
                  AND technology_assignment != ''
                  AND is_relevant = 1
                GROUP BY technology_assignment
                ORDER BY count DESC, technology_assignment ASC
                LIMIT ?";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(['%' . $query . '%', $limit]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Prepare search term for full-text search
     */
    private function prepareSearchTerm($query) {
        // Clean and prepare the search term
        $term = trim($query);
        
        // Add wildcard for partial matches
        if (strlen($term) >= 3) {
            $term = '+' . $term . '*';
        }
        
        return $term;
    }
    
    /**
     * Build filter conditions for search queries
     */
    private function buildFilterConditions($filters, &$params, $table_alias = 'o') {
        $conditions = [];
        
        if (!empty($filters['sales_rep'])) {
            $conditions[] = "{$table_alias}.sales_rep = ?";
            $params[] = $filters['sales_rep'];
        }
        
        if (!empty($filters['preview_status'])) {
            if (is_array($filters['preview_status'])) {
                $placeholders = str_repeat('?,', count($filters['preview_status']) - 1) . '?';
                $conditions[] = "{$table_alias}.preview_status IN ($placeholders)";
                $params = array_merge($params, $filters['preview_status']);
            } else {
                $conditions[] = "{$table_alias}.preview_status = ?";
                $params[] = $filters['preview_status'];
            }
        }
        
        if (!empty($filters['date_from'])) {
            $conditions[] = "{$table_alias}.order_date >= ?";
            $params[] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $conditions[] = "{$table_alias}.order_date <= ?";
            $params[] = $filters['date_to'];
        }
        
        if (isset($filters['is_completed'])) {
            $conditions[] = "{$table_alias}.is_completed = ?";
            $params[] = $filters['is_completed'] ? 1 : 0;
        }
        
        return $conditions;
    }
    
    /**
     * Log search analytics
     */
    private function logSearchAnalytics($query, $type, $results_count, $execution_time) {
        try {
            $user_id = $_SESSION['user_id'] ?? null;
            
            $sql = "INSERT INTO search_analytics 
                    (user_id, search_query, search_type, results_count, execution_time_ms) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$user_id, $query, $type, $results_count, $execution_time]);
        } catch (Exception $e) {
            // Log error but don't break search functionality
            error_log("Search analytics logging failed: " . $e->getMessage());
        }
    }
}
