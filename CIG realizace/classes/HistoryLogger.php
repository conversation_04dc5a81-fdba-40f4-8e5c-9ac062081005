<?php
/**
 * HistoryLogger Class - Centralized order history logging
 * CIG Realizace - Phase 06
 */

require_once __DIR__ . '/../config/database.php';

class HistoryLogger {
    
    /**
     * Log a general order change
     */
    public static function log($orderId, $userId, $actionType, $oldValue, $newValue, $description, $additionalData = null) {
        try {
            $pdo = getDbConnection();
            
            // Get user info for better logging
            $stmt = $pdo->prepare("SELECT username, full_name FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();
            
            // Prepare additional data as JSON if provided
            $additionalDataJson = $additionalData ? json_encode($additionalData, JSON_UNESCAPED_UNICODE) : null;
            
            $stmt = $pdo->prepare("
                INSERT INTO order_history (
                    order_id, user_id, action_type, old_value, new_value, 
                    description, additional_data, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $orderId,
                $userId,
                $actionType,
                $oldValue,
                $newValue,
                $description,
                $additionalDataJson,
                date('Y-m-d H:i:s')
            ]);
            
            // Create notification for relevant users
            self::createNotification($orderId, $userId, $actionType, $description);
            
            return true;
            
        } catch (Exception $e) {
            error_log("HistoryLogger error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log preview status change
     */
    public static function logPreviewStatusChange($orderId, $userId, $oldStatus, $newStatus, $notes = null) {
        $statusLabels = [
            'not_created' => 'Nevytvořen',
            'sent_to_client' => 'Odeslán klientovi',
            'approved' => 'Schválen'
        ];
        
        $description = "Stav náhledu změněn z '{$statusLabels[$oldStatus]}' na '{$statusLabels[$newStatus]}'";
        if (!empty($notes)) {
            $description .= ". Poznámka: " . $notes;
        }
        
        $additionalData = [
            'old_status_label' => $statusLabels[$oldStatus],
            'new_status_label' => $statusLabels[$newStatus],
            'notes' => $notes
        ];
        
        return self::log($orderId, $userId, 'preview_status_change', $oldStatus, $newStatus, $description, $additionalData);
    }
    
    /**
     * Log delivery date change
     */
    public static function logDeliveryDateChange($orderId, $userId, $oldDate, $newDate) {
        $oldDateFormatted = $oldDate ? date('d.m.Y', strtotime($oldDate)) : 'Nenastaveno';
        $newDateFormatted = date('d.m.Y', strtotime($newDate));
        
        $description = "Termín dodání změněn z '{$oldDateFormatted}' na '{$newDateFormatted}'";
        
        $additionalData = [
            'old_date_formatted' => $oldDateFormatted,
            'new_date_formatted' => $newDateFormatted
        ];
        
        return self::log($orderId, $userId, 'delivery_date_change', $oldDate, $newDate, $description, $additionalData);
    }
    
    /**
     * Log technology assignment
     */
    public static function logTechnologyAssignment($orderId, $userId, $itemId, $catalogCode, $oldTechnology, $newTechnology) {
        $description = "Technologie přiřazena k položce '{$catalogCode}': '{$newTechnology}'";
        if ($oldTechnology) {
            $description = "Technologie změněna u položky '{$catalogCode}' z '{$oldTechnology}' na '{$newTechnology}'";
        }
        
        $additionalData = [
            'item_id' => $itemId,
            'catalog_code' => $catalogCode
        ];
        
        return self::log($orderId, $userId, 'technology_assignment', $oldTechnology, $newTechnology, $description, $additionalData);
    }
    
    /**
     * Log item relevance change
     */
    public static function logItemRelevanceChange($orderId, $userId, $itemId, $catalogCode, $isRelevant) {
        $status = $isRelevant ? 'relevantní' : 'irelevantní';
        $description = "Položka '{$catalogCode}' označena jako {$status}";
        
        $additionalData = [
            'item_id' => $itemId,
            'catalog_code' => $catalogCode
        ];
        
        return self::log($orderId, $userId, 'item_relevance_change', !$isRelevant, $isRelevant, $description, $additionalData);
    }
    
    /**
     * Log inventory status change
     */
    public static function logInventoryStatusChange($orderId, $userId, $itemId, $catalogCode, $oldStatus, $newStatus) {
        $statusLabels = [
            'not_in_stock' => 'Není skladem',
            'ordered' => 'Objednáno',
            'in_stock' => 'Skladem'
        ];
        
        $description = "Stav zásob položky '{$catalogCode}' změněn z '{$statusLabels[$oldStatus]}' na '{$statusLabels[$newStatus]}'";
        
        $additionalData = [
            'item_id' => $itemId,
            'catalog_code' => $catalogCode,
            'old_status_label' => $statusLabels[$oldStatus],
            'new_status_label' => $statusLabels[$newStatus]
        ];
        
        return self::log($orderId, $userId, 'inventory_status_change', $oldStatus, $newStatus, $description, $additionalData);
    }
    
    /**
     * Log order completion
     */
    public static function logOrderCompletion($orderId, $userId, $notes = null) {
        $description = "Objednávka byla dokončena";
        if (!empty($notes)) {
            $description .= ". Poznámka: " . $notes;
        }
        
        $additionalData = [
            'notes' => $notes,
            'completed_at' => date('Y-m-d H:i:s')
        ];
        
        return self::log($orderId, $userId, 'order_completion', 'in_progress', 'completed', $description, $additionalData);
    }
    
    /**
     * Get order history with user information
     */
    public static function getOrderHistory($orderId, $limit = null, $actionType = null) {
        try {
            $pdo = getDbConnection();
            
            $sql = "
                SELECT oh.*, u.username, u.full_name, u.role
                FROM order_history oh
                LEFT JOIN users u ON oh.user_id = u.id
                WHERE oh.order_id = ?
            ";
            
            $params = [$orderId];
            
            if ($actionType) {
                $sql .= " AND oh.action_type = ?";
                $params[] = $actionType;
            }
            
            $sql .= " ORDER BY oh.created_at DESC";
            
            if ($limit) {
                $sql .= " LIMIT ?";
                $params[] = $limit;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting order history: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get recent system activity
     */
    public static function getRecentActivity($limit = 20, $userId = null) {
        try {
            $pdo = getDbConnection();
            
            $sql = "
                SELECT oh.*, u.username, u.full_name, u.role, o.order_code
                FROM order_history oh
                LEFT JOIN users u ON oh.user_id = u.id
                LEFT JOIN orders o ON oh.order_id = o.id
                WHERE 1=1
            ";
            
            $params = [];
            
            if ($userId) {
                $sql .= " AND oh.user_id = ?";
                $params[] = $userId;
            }
            
            $sql .= " ORDER BY oh.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting recent activity: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Create notification for relevant users
     */
    private static function createNotification($orderId, $userId, $actionType, $description) {
        try {
            $pdo = getDbConnection();
            
            // Get order info
            $stmt = $pdo->prepare("SELECT order_code, sales_rep_id FROM orders WHERE id = ?");
            $stmt->execute([$orderId]);
            $order = $stmt->fetch();
            
            if (!$order) return;
            
            // Determine who should be notified based on action type
            $notifyUsers = [];
            
            switch ($actionType) {
                case 'preview_status_change':
                    // Notify sales rep and admin
                    if ($order['sales_rep_id']) {
                        $notifyUsers[] = $order['sales_rep_id'];
                    }
                    break;
                    
                case 'order_completion':
                    // Notify sales rep
                    if ($order['sales_rep_id']) {
                        $notifyUsers[] = $order['sales_rep_id'];
                    }
                    break;
            }
            
            // Add admin to all notifications (except if admin made the change)
            $stmt = $pdo->prepare("SELECT id FROM users WHERE role = 'admin' AND id != ?");
            $stmt->execute([$userId]);
            $admins = $stmt->fetchAll();
            foreach ($admins as $admin) {
                $notifyUsers[] = $admin['id'];
            }
            
            // Remove duplicates and the user who made the change
            $notifyUsers = array_unique($notifyUsers);
            $notifyUsers = array_filter($notifyUsers, function($id) use ($userId) {
                return $id != $userId;
            });
            
            // Create notifications
            foreach ($notifyUsers as $notifyUserId) {
                $stmt = $pdo->prepare("
                    INSERT INTO notifications (
                        user_id, order_id, type, title, message, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $title = "Změna objednávky {$order['order_code']}";
                
                $stmt->execute([
                    $notifyUserId,
                    $orderId,
                    $actionType,
                    $title,
                    $description,
                    date('Y-m-d H:i:s')
                ]);
            }
            
        } catch (Exception $e) {
            error_log("Error creating notification: " . $e->getMessage());
        }
    }
}
?>
