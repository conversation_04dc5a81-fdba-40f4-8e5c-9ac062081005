<?php
/**
 * Authentication Class
 * Handles user authentication, session management, and access control
 */
class Auth {
    private $pdo;
    private $user;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->user = new User($pdo);
    }
    
    /**
     * Login user
     */
    public function login($username, $password) {
        try {
            $user = $this->user->verifyPassword($username, $password);
            
            if ($user) {
                // Regenerate session ID for security
                session_regenerate_id(true);
                
                // Set session variables
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['full_name'] = $user['full_name'];
                $_SESSION['login_time'] = time();
                $_SESSION['last_activity'] = time();
                
                // Update last login time
                $this->updateLastLogin($user['id']);
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('Login error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Logout user
     */
    public function logout() {
        // Destroy session data
        session_unset();
        session_destroy();
        
        // Start new session for messages
        session_start();
        $_SESSION['logout_message'] = 'Byli jste úspěšně odhlášeni.';
    }
    
    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        return isset($_SESSION['user_id']) && $this->isSessionValid();
    }
    
    /**
     * Check if session is valid (not expired)
     */
    public function isSessionValid() {
        $session_timeout = 3600; // 1 hour
        
        if (isset($_SESSION['last_activity'])) {
            if (time() - $_SESSION['last_activity'] > $session_timeout) {
                $this->logout();
                return false;
            }
            $_SESSION['last_activity'] = time();
        }
        
        return true;
    }
    
    /**
     * Get current user data
     */
    public function getCurrentUser() {
        if ($this->isLoggedIn()) {
            return [
                'id' => $_SESSION['user_id'],
                'username' => $_SESSION['username'],
                'role' => $_SESSION['role'],
                'full_name' => $_SESSION['full_name'],
                'login_time' => $_SESSION['login_time']
            ];
        }
        return null;
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        return $this->isLoggedIn() && $_SESSION['role'] === $role;
    }
    
    /**
     * Check if user has any of the specified roles
     */
    public function hasAnyRole($roles) {
        if (!$this->isLoggedIn()) {
            return false;
        }
        
        if (is_string($roles)) {
            $roles = [$roles];
        }
        
        return in_array($_SESSION['role'], $roles);
    }
    
    /**
     * Require login - redirect to login page if not logged in
     */
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: index.php');
            exit;
        }
    }
    
    /**
     * Require specific role - show error if user doesn't have required role
     */
    public function requireRole($role) {
        $this->requireLogin();
        
        if (!$this->hasRole($role)) {
            $this->showAccessDenied();
        }
    }
    
    /**
     * Require any of the specified roles
     */
    public function requireAnyRole($roles) {
        $this->requireLogin();
        
        if (!$this->hasAnyRole($roles)) {
            $this->showAccessDenied();
        }
    }
    
    /**
     * Show access denied page
     */
    public function showAccessDenied() {
        http_response_code(403);
        include 'includes/access_denied.php';
        exit;
    }
    
    /**
     * Update last login time
     */
    private function updateLastLogin($user_id) {
        try {
            $stmt = $this->pdo->prepare("UPDATE users SET last_login = datetime('now') WHERE id = ?");
            $stmt->execute([$user_id]);
        } catch (PDOException $e) {
            error_log('Error updating last login: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate CSRF token
     */
    public function generateCSRFToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Get role permissions
     */
    public function getRolePermissions($role = null) {
        if ($role === null) {
            $role = $_SESSION['role'] ?? null;
        }
        
        $permissions = [
            'admin' => [
                'users_manage',
                'orders_view_all',
                'orders_edit_all',
                'orders_delete',
                'import_csv',
                'view_statistics',
                'system_settings'
            ],
            'obchodnik' => [
                'orders_view_own',
                'orders_edit_own',
                'previews_manage',
                'import_csv',
                'calendar_view'
            ],
            'grafik' => [
                'orders_view_graphics',
                'previews_create',
                'previews_edit',
                'calendar_view'
            ],
            'realizator' => [
                'orders_view_approved',
                'orders_mark_completed',
                'production_manage',
                'calendar_view'
            ]
        ];
        
        return $permissions[$role] ?? [];
    }
    
    /**
     * Check if user has specific permission
     */
    public function hasPermission($permission) {
        $permissions = $this->getRolePermissions();
        return in_array($permission, $permissions);
    }
}
?>
