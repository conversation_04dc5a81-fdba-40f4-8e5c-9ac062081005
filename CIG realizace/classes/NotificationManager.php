<?php
/**
 * NotificationManager Class - Manage user notifications
 * CIG Realizace - Phase 06
 */

require_once __DIR__ . '/../config/database.php';

class NotificationManager {
    
    /**
     * Create a new notification
     */
    public static function create($userId, $orderId, $type, $title, $message) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                INSERT INTO notifications (user_id, order_id, type, title, message, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $orderId,
                $type,
                $title,
                $message,
                date('Y-m-d H:i:s')
            ]);
            
            return $pdo->lastInsertId();
            
        } catch (Exception $e) {
            error_log("NotificationManager create error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get user notifications
     */
    public static function getUserNotifications($userId, $limit = 20, $unreadOnly = false) {
        try {
            $pdo = getDbConnection();
            
            $sql = "
                SELECT n.*, o.order_code
                FROM notifications n
                LEFT JOIN orders o ON n.order_id = o.id
                WHERE n.user_id = ?
            ";
            
            $params = [$userId];
            
            if ($unreadOnly) {
                $sql .= " AND n.is_read = 0";
            }
            
            $sql .= " ORDER BY n.created_at DESC LIMIT ?";
            $params[] = $limit;
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetchAll();
            
        } catch (Exception $e) {
            error_log("Error getting user notifications: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get unread notifications count
     */
    public static function getUnreadCount($userId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count
                FROM notifications
                WHERE user_id = ? AND is_read = 0
            ");
            
            $stmt->execute([$userId]);
            $result = $stmt->fetch();
            
            return $result['count'] ?? 0;
            
        } catch (Exception $e) {
            error_log("Error getting unread count: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Mark notification as read
     */
    public static function markAsRead($notificationId, $userId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                UPDATE notifications 
                SET is_read = 1, read_at = ?
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([
                date('Y-m-d H:i:s'),
                $notificationId,
                $userId
            ]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log("Error marking notification as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Mark all notifications as read for user
     */
    public static function markAllAsRead($userId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                UPDATE notifications 
                SET is_read = 1, read_at = ?
                WHERE user_id = ? AND is_read = 0
            ");
            
            $stmt->execute([
                date('Y-m-d H:i:s'),
                $userId
            ]);
            
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            error_log("Error marking all notifications as read: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete notification
     */
    public static function delete($notificationId, $userId) {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                DELETE FROM notifications 
                WHERE id = ? AND user_id = ?
            ");
            
            $stmt->execute([$notificationId, $userId]);
            
            return $stmt->rowCount() > 0;
            
        } catch (Exception $e) {
            error_log("Error deleting notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean old notifications (older than 30 days)
     */
    public static function cleanOldNotifications() {
        try {
            $pdo = getDbConnection();
            
            $stmt = $pdo->prepare("
                DELETE FROM notifications 
                WHERE created_at < DATE('now', '-30 days')
            ");
            
            $stmt->execute();
            
            return $stmt->rowCount();
            
        } catch (Exception $e) {
            error_log("Error cleaning old notifications: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get notification statistics
     */
    public static function getStatistics($userId = null) {
        try {
            $pdo = getDbConnection();
            
            $sql = "
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN is_read = 1 THEN 1 ELSE 0 END) as read,
                    COUNT(DISTINCT type) as types_count
                FROM notifications
            ";
            
            $params = [];
            
            if ($userId) {
                $sql .= " WHERE user_id = ?";
                $params[] = $userId;
            }
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (Exception $e) {
            error_log("Error getting notification statistics: " . $e->getMessage());
            return [
                'total' => 0,
                'unread' => 0,
                'read' => 0,
                'types_count' => 0
            ];
        }
    }
    
    /**
     * Format notification for display
     */
    public static function formatNotification($notification) {
        $timeAgo = self::timeAgo($notification['created_at']);
        
        $icon = self::getNotificationIcon($notification['type']);
        $color = self::getNotificationColor($notification['type']);
        
        return [
            'id' => $notification['id'],
            'title' => $notification['title'],
            'message' => $notification['message'],
            'order_code' => $notification['order_code'],
            'order_id' => $notification['order_id'],
            'type' => $notification['type'],
            'is_read' => $notification['is_read'],
            'created_at' => $notification['created_at'],
            'time_ago' => $timeAgo,
            'icon' => $icon,
            'color' => $color
        ];
    }
    
    /**
     * Get notification icon based on type
     */
    private static function getNotificationIcon($type) {
        $icons = [
            'preview_status_change' => 'fas fa-eye',
            'delivery_date_change' => 'fas fa-calendar-alt',
            'technology_assignment' => 'fas fa-cogs',
            'item_relevance_change' => 'fas fa-toggle-on',
            'inventory_status_change' => 'fas fa-boxes',
            'order_completion' => 'fas fa-check-circle',
            'system' => 'fas fa-info-circle'
        ];
        
        return $icons[$type] ?? 'fas fa-bell';
    }
    
    /**
     * Get notification color based on type
     */
    private static function getNotificationColor($type) {
        $colors = [
            'preview_status_change' => 'primary',
            'delivery_date_change' => 'warning',
            'technology_assignment' => 'info',
            'item_relevance_change' => 'secondary',
            'inventory_status_change' => 'success',
            'order_completion' => 'success',
            'system' => 'info'
        ];
        
        return $colors[$type] ?? 'secondary';
    }
    
    /**
     * Calculate time ago
     */
    private static function timeAgo($datetime) {
        $time = time() - strtotime($datetime);
        
        if ($time < 60) return 'Právě teď';
        if ($time < 3600) return floor($time/60) . ' min';
        if ($time < 86400) return floor($time/3600) . ' hod';
        if ($time < 2592000) return floor($time/86400) . ' dní';
        if ($time < 31536000) return floor($time/2592000) . ' měs';
        
        return floor($time/31536000) . ' let';
    }
}
?>
