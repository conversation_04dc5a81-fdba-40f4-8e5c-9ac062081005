<?php
/**
 * SQL skript pro vytvoření tabulky dotazů na cenu
 */

$sql = array();

$sql[] = 'CREATE TABLE IF NOT EXISTS `'._DB_PREFIX_.'price_inquiry` (
    `id_inquiry` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `id_product` int(10) unsigned NOT NULL,
    `id_product_attribute` int(10) unsigned DEFAULT 0,
    `id_customer` int(10) unsigned DEFAULT 0,
    `customer_name` varchar(255) NOT NULL,
    `customer_email` varchar(255) NOT NULL,
    `customer_phone` varchar(50) DEFAULT NULL,
    `product_name` varchar(255) NOT NULL,
    `product_reference` varchar(64) DEFAULT NULL,
    `message` text DEFAULT NULL,
    `date_add` datetime NOT NULL,
    `resolved` tinyint(1) NOT NULL DEFAULT 0,
    `date_resolved` datetime DEFAULT NULL,
    PRIMARY KEY (`id_inquiry`),
    KEY `id_product` (`id_product`),
    <PERSON><PERSON>Y `id_customer` (`id_customer`),
    <PERSON><PERSON><PERSON> `resolved` (`resolved`),
    <PERSON><PERSON>Y `date_add` (`date_add`)
) ENGINE='._MYSQL_ENGINE_.' DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;';

// Aktualizace existující tabulky pro přidání nových sloupců
$sql[] = 'ALTER TABLE `'._DB_PREFIX_.'price_inquiry`
    ADD COLUMN IF NOT EXISTS `product_name` varchar(255) NOT NULL DEFAULT "",
    ADD COLUMN IF NOT EXISTS `product_reference` varchar(64) DEFAULT NULL,
    ADD COLUMN IF NOT EXISTS `resolved` tinyint(1) NOT NULL DEFAULT 0,
    ADD COLUMN IF NOT EXISTS `date_resolved` datetime DEFAULT NULL';

// Odstranění starého sloupce status pokud existuje
$sql[] = 'ALTER TABLE `'._DB_PREFIX_.'price_inquiry` DROP COLUMN IF EXISTS `status`';

foreach ($sql as $query) {
    if (Db::getInstance()->execute($query) == false) {
        return false;
    }
}