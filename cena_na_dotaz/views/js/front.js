/**
 * JavaScript pro frontend modul "Cena na dotaz"
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Inicializace modulu
    PriceInquiry.init();
    
});

/**
 * Hlavní objekt pro správu dotazů na cenu
 */
var PriceInquiry = {
    
    /**
     * Inicializace modulu
     */
    init: function() {
        this.bindEvents();
        this.hideAddToCartButtons();
    },
    
    /**
     * Navázání event listenerů
     */
    bindEvents: function() {
        // Kliknutí na tlačítko "Zjistit cenu"
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('price-inquiry-btn') ||
                e.target.closest('.price-inquiry-btn')) {

                e.preventDefault();
                e.stopPropagation();

                var button = e.target.classList.contains('price-inquiry-btn') ?
                           e.target : e.target.closest('.price-inquiry-btn');

                PriceInquiry.handleInquiryClick(button);
            }

            // Zavření modalu
            if (e.target.classList.contains('modal-close') ||
                e.target.closest('.modal-close') ||
                e.target.classList.contains('modal-overlay')) {

                e.preventDefault();
                PriceInquiry.closeModal();
            }
        });

        // Zavření modalu klávesou ESC
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && document.getElementById('priceInquiryModal').style.display !== 'none') {
                PriceInquiry.closeModal();
            }
        });

        // Odeslání formuláře
        document.addEventListener('submit', function(e) {
            if (e.target.id === 'priceInquiryForm') {
                e.preventDefault();
                PriceInquiry.submitForm();
            }
        });
    },
    
    /**
     * Zpracování kliknutí na tlačítko "Zjistit cenu"
     */
    handleInquiryClick: function(button) {
        var productId = button.getAttribute('data-product-id');
        var productName = button.getAttribute('data-product-name');
        var productReference = button.getAttribute('data-product-reference') || '';
        var productImage = button.getAttribute('data-product-image') || '';
        var isLogged = button.getAttribute('data-is-logged') === '1';
        var customerName = button.getAttribute('data-customer-name') || '';
        var customerEmail = button.getAttribute('data-customer-email') || '';

        // Zobrazení loading stavu
        this.setButtonLoading(button, true);

        // Otevření popup formuláře
        setTimeout(function() {
            PriceInquiry.setButtonLoading(button, false);
            PriceInquiry.openModal(productId, productName, productReference, productImage, isLogged, customerName, customerEmail);
        }, 300);
    },
    
    /**
     * Nastavení loading stavu tlačítka
     */
    setButtonLoading: function(button, loading) {
        if (loading) {
            button.disabled = true;
            button.classList.add('loading');
            
            var originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<i class="material-icons">hourglass_empty</i> Načítání...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            
            var originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    },
    
    /**
     * Skrytí tlačítek "Přidat do košíku" pro produkty s cenou 0
     */
    hideAddToCartButtons: function() {
        // Najdeme všechny produkty s cenou na dotaz
        var priceInquiryContainers = document.querySelectorAll('.price-inquiry-container');
        
        priceInquiryContainers.forEach(function(container) {
            // Najdeme nejbližší product container
            var productContainer = container.closest('.product-miniature, .product-container, .product, .js-product');
            
            if (productContainer) {
                // Skryjeme všechna tlačítka pro přidání do košíku v tomto produktu
                var addToCartButtons = productContainer.querySelectorAll(
                    '.add-to-cart, .product-add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
                );
                
                addToCartButtons.forEach(function(btn) {
                    btn.style.display = 'none';
                });
            }
        });
        
        // Také skryjeme tlačítka na stránce produktu
        if (document.querySelector('.price-inquiry-btn')) {
            var productPageButtons = document.querySelectorAll(
                '.product-add-to-cart, .add-to-cart, .btn-add-to-cart, [data-button-action="add-to-cart"]'
            );
            
            productPageButtons.forEach(function(btn) {
                btn.style.display = 'none';
            });
        }
    },

    /**
     * Otevření popup modalu
     */
    openModal: function(productId, productName, productReference, productImage, isLogged, customerName, customerEmail) {
        var modal = document.getElementById('priceInquiryModal');
        if (!modal) {
            console.error('Modal element not found');
            return;
        }

        // Vyplnění informací o produktu
        document.getElementById('modalProductId').value = productId;
        document.getElementById('modalProductNameHidden').value = productName;
        document.getElementById('modalProductReferenceHidden').value = productReference;
        document.getElementById('modalProductName').textContent = productName;
        document.getElementById('modalProductReference').textContent = productReference;

        if (productImage) {
            document.getElementById('modalProductImage').src = productImage;
            document.getElementById('modalProductImage').alt = productName;
        }

        // Vyplnění údajů přihlášeného uživatele
        if (isLogged) {
            document.getElementById('customer_name').value = customerName;
            document.getElementById('customer_email').value = customerEmail;
        }

        // Vymazání předchozích zpráv
        this.hideMessages();

        // Zobrazení modalu
        modal.style.display = 'block';
        document.body.classList.add('modal-open');

        // Focus na první pole
        setTimeout(function() {
            var firstInput = modal.querySelector('input:not([type="hidden"]):not([readonly])');
            if (firstInput) {
                firstInput.focus();
            }
        }, 100);
    },

    /**
     * Zavření popup modalu
     */
    closeModal: function() {
        var modal = document.getElementById('priceInquiryModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.classList.remove('modal-open');

            // Reset formuláře
            var form = document.getElementById('priceInquiryForm');
            if (form) {
                form.reset();
            }

            this.hideMessages();
        }
    },

    /**
     * Odeslání formuláře pomocí AJAX
     */
    submitForm: function() {
        var form = document.getElementById('priceInquiryForm');
        var submitBtn = document.getElementById('submitInquiry');

        // Validace formuláře
        if (!this.validateForm()) {
            return;
        }

        // Nastavení loading stavu
        this.setSubmitLoading(true);
        this.hideMessages();

        // Příprava dat
        var formData = new FormData(form);

        // AJAX požadavek
        var xhr = new XMLHttpRequest();
        xhr.open('POST', form.action, true);

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                PriceInquiry.setSubmitLoading(false);

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        PriceInquiry.handleResponse(response);
                    } catch (e) {
                        PriceInquiry.showError('Došlo k chybě při zpracování odpovědi.');
                    }
                } else {
                    PriceInquiry.showError('Došlo k chybě při odesílání dotazu. Zkuste to prosím znovu.');
                }
            }
        };

        xhr.send(formData);
    },

    /**
     * Validace formuláře
     */
    validateForm: function() {
        var errors = [];

        // Jméno
        var name = document.getElementById('customer_name').value.trim();
        if (!name) {
            errors.push('Jméno a příjmení je povinné pole.');
        }

        // E-mail
        var email = document.getElementById('customer_email').value.trim();
        if (!email) {
            errors.push('E-mailová adresa je povinná.');
        } else if (!this.isValidEmail(email)) {
            errors.push('Zadejte platnou e-mailovou adresu.');
        }

        // GDPR souhlas
        var gdprConsent = document.getElementById('gdpr_consent').checked;
        if (!gdprConsent) {
            errors.push('Musíte souhlasit se zpracováním osobních údajů.');
        }

        if (errors.length > 0) {
            this.showError(errors.join('<br>'));
            return false;
        }

        return true;
    },

    /**
     * Validace e-mailové adresy
     */
    isValidEmail: function(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * Zpracování odpovědi ze serveru
     */
    handleResponse: function(response) {
        if (response.success) {
            this.showSuccess(response.message);

            // Zavření modalu po 2 sekundách
            setTimeout(function() {
                PriceInquiry.closeModal();
            }, 2000);
        } else {
            if (response.errors && response.errors.length > 0) {
                this.showError(response.errors.join('<br>'));
            } else {
                this.showError(response.message || 'Došlo k neočekávané chybě.');
            }
        }
    },

    /**
     * Zobrazení chybové zprávy
     */
    showError: function(message) {
        var messagesContainer = document.getElementById('inquiryMessages');
        var errorDiv = document.getElementById('inquiryError');

        errorDiv.innerHTML = message;
        errorDiv.style.display = 'block';
        document.getElementById('inquirySuccess').style.display = 'none';
        messagesContainer.style.display = 'block';

        // Scroll k chybě
        errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    },

    /**
     * Zobrazení úspěšné zprávy
     */
    showSuccess: function(message) {
        var messagesContainer = document.getElementById('inquiryMessages');
        var successDiv = document.getElementById('inquirySuccess');

        successDiv.innerHTML = message;
        successDiv.style.display = 'block';
        document.getElementById('inquiryError').style.display = 'none';
        messagesContainer.style.display = 'block';

        // Scroll k úspěchu
        successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    },

    /**
     * Skrytí všech zpráv
     */
    hideMessages: function() {
        var messagesContainer = document.getElementById('inquiryMessages');
        if (messagesContainer) {
            messagesContainer.style.display = 'none';
            document.getElementById('inquiryError').style.display = 'none';
            document.getElementById('inquirySuccess').style.display = 'none';
        }
    },

    /**
     * Nastavení loading stavu pro submit tlačítko
     */
    setSubmitLoading: function(loading) {
        var submitBtn = document.getElementById('submitInquiry');
        var btnText = submitBtn.querySelector('.btn-text');
        var btnLoading = submitBtn.querySelector('.btn-loading');

        if (loading) {
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline';
        } else {
            submitBtn.disabled = false;
            btnText.style.display = 'inline';
            btnLoading.style.display = 'none';
        }
    }

};

/**
 * CSS styly pro loading stav (přidáno dynamicky)
 */
if (!document.querySelector('#price-inquiry-dynamic-styles')) {
    var style = document.createElement('style');
    style.id = 'price-inquiry-dynamic-styles';
    style.textContent = `
        .price-inquiry-btn.loading {
            opacity: 0.7;
            cursor: not-allowed;
        }
        
        .price-inquiry-btn.loading:hover {
            transform: none;
            box-shadow: none;
        }
    `;
    document.head.appendChild(style);
}
