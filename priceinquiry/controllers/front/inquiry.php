<?php
/**
 * Frontend controller pro zpracování dotazů na cenu
 * Zpracovává AJAX požadavky z popup formuláře
 */

class PriceInquiryInquiryModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $display_column_left = false;
    public $display_column_right = false;
    public $display_header = false;
    public $display_footer = false;

    /**
     * Inicializace controlleru
     */
    public function init()
    {
        parent::init();
        
        // Kontrola, zda je požadavek AJAX
        if (!Tools::isSubmit('ajax')) {
            Tools::redirect('index.php');
        }
    }

    /**
     * Zpracování POST požadavku
     */
    public function postProcess()
    {
        if (Tools::isSubmit('ajax') && Tools::getValue('ajax') == '1') {
            $this->processAjaxInquiry();
        }
    }

    /**
     * Zpracování AJAX dotazu na cenu
     */
    private function processAjaxInquiry()
    {
        $response = array(
            'success' => false,
            'message' => '',
            'errors' => array()
        );

        try {
            // Validace vstupních dat
            $validation = $this->validateInquiryData();
            if (!$validation['valid']) {
                $response['errors'] = $validation['errors'];
                $this->ajaxDie(json_encode($response));
            }

            // Získání dat z formuláře
            $inquiryData = $this->getInquiryData();
            
            // Uložení dotazu do databáze
            $inquiryId = $this->saveInquiry($inquiryData);
            
            if ($inquiryId) {
                // Odeslání e-mailových notifikací
                $this->sendNotifications($inquiryId, $inquiryData);
                
                $response['success'] = true;
                $response['message'] = $this->module->l('Váš dotaz byl úspěšně odeslán. Brzy se Vám ozveme s nabídkou.', 'inquiry');
                $response['inquiry_id'] = $inquiryId;
            } else {
                $response['message'] = $this->module->l('Došlo k chybě při ukládání dotazu. Zkuste to prosím znovu.', 'inquiry');
            }

        } catch (Exception $e) {
            $response['message'] = $this->module->l('Došlo k neočekávané chybě. Zkuste to prosím znovu.', 'inquiry');
            PrestaShopLogger::addLog('PriceInquiry Error: ' . $e->getMessage(), 3);
        }

        $this->ajaxDie(json_encode($response));
    }

    /**
     * Validace dat z formuláře
     */
    private function validateInquiryData()
    {
        $errors = array();
        
        // Povinná pole
        if (!Tools::getValue('customer_name') || trim(Tools::getValue('customer_name')) == '') {
            $errors[] = $this->module->l('Jméno a příjmení je povinné pole.', 'inquiry');
        }
        
        if (!Tools::getValue('customer_email') || !Validate::isEmail(Tools::getValue('customer_email'))) {
            $errors[] = $this->module->l('Zadejte platnou e-mailovou adresu.', 'inquiry');
        }
        
        if (!Tools::getValue('id_product') || !Validate::isUnsignedId(Tools::getValue('id_product'))) {
            $errors[] = $this->module->l('Neplatný produkt.', 'inquiry');
        }
        
        // GDPR souhlas
        if (!Tools::getValue('gdpr_consent')) {
            $errors[] = $this->module->l('Musíte souhlasit se zpracováním osobních údajů.', 'inquiry');
        }
        
        // Kontrola existence produktu
        $productId = (int)Tools::getValue('id_product');
        $product = new Product($productId, false, $this->context->language->id);
        if (!Validate::isLoadedObject($product)) {
            $errors[] = $this->module->l('Produkt nebyl nalezen.', 'inquiry');
        }
        
        // Volitelná validace telefonu
        $phone = Tools::getValue('customer_phone');
        if ($phone && !empty(trim($phone)) && !Validate::isPhoneNumber($phone)) {
            $errors[] = $this->module->l('Zadejte platné telefonní číslo.', 'inquiry');
        }
        
        // Validace množství
        $quantity = (int)Tools::getValue('quantity');
        if ($quantity < 1) {
            $errors[] = $this->module->l('Množství musí být alespoň 1.', 'inquiry');
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors
        );
    }

    /**
     * Získání a příprava dat z formuláře
     */
    private function getInquiryData()
    {
        return array(
            'id_product' => (int)Tools::getValue('id_product'),
            'product_name' => pSQL(Tools::getValue('product_name')),
            'product_reference' => pSQL(Tools::getValue('product_reference')),
            'customer_name' => pSQL(Tools::getValue('customer_name')),
            'customer_email' => pSQL(Tools::getValue('customer_email')),
            'customer_phone' => pSQL(Tools::getValue('customer_phone')),
            'message' => pSQL(Tools::getValue('message')),
            'id_customer' => $this->context->customer->isLogged() ? (int)$this->context->customer->id : 0,
            'date_add' => date('Y-m-d H:i:s'),
            'resolved' => 0
        );
    }

    /**
     * Uložení dotazu do databáze
     */
    private function saveInquiry($data)
    {
        $sql = 'INSERT INTO `' . _DB_PREFIX_ . 'price_inquiry` (
            `id_product`, `product_name`, `product_reference`,
            `customer_name`, `customer_email`, `customer_phone`,
            `message`, `id_customer`, `date_add`, `resolved`
        ) VALUES (
            ' . (int)$data['id_product'] . ',
            "' . $data['product_name'] . '",
            "' . $data['product_reference'] . '",
            "' . $data['customer_name'] . '",
            "' . $data['customer_email'] . '",
            "' . $data['customer_phone'] . '",
            "' . $data['message'] . '",
            ' . (int)$data['id_customer'] . ',
            "' . $data['date_add'] . '",
            ' . (int)$data['resolved'] . '
        )';

        if (Db::getInstance()->execute($sql)) {
            return Db::getInstance()->Insert_ID();
        }

        return false;
    }

    /**
     * Odeslání e-mailových notifikací
     */
    private function sendNotifications($inquiryId, $data)
    {
        try {
            $emailsSent = array();

            // Odeslání e-mailu administrátorovi (pokud je povoleno)
            if (Configuration::get('PRICE_INQUIRY_SEND_ADMIN_EMAIL')) {
                if ($this->sendAdminNotification($inquiryId, $data)) {
                    $emailsSent[] = 'admin';
                }
            }

            // Odeslání potvrzovacího e-mailu zákazníkovi (pokud je povoleno)
            if (Configuration::get('PRICE_INQUIRY_SEND_CUSTOMER_EMAIL')) {
                if ($this->sendCustomerConfirmation($inquiryId, $data)) {
                    $emailsSent[] = 'customer';
                }
            }

            if (!empty($emailsSent)) {
                PrestaShopLogger::addLog('PriceInquiry: Email notifications sent (' . implode(', ', $emailsSent) . ') for inquiry #' . $inquiryId, 1);
            }

        } catch (Exception $e) {
            PrestaShopLogger::addLog('PriceInquiry Email Error: ' . $e->getMessage(), 3);
        }
    }

    /**
     * Odeslání notifikace administrátorovi o novém dotazu
     */
    private function sendAdminNotification($inquiryId, $data)
    {
        $adminEmail = Configuration::get('PRICE_INQUIRY_ADMIN_EMAIL');
        if (!$adminEmail || !Validate::isEmail($adminEmail)) {
            return false;
        }

        // Příprava dat pro e-mail
        $templateVars = array(
            '{inquiry_id}' => $inquiryId,
            '{customer_name}' => $data['customer_name'],
            '{customer_email}' => $data['customer_email'],
            '{customer_phone}' => $data['customer_phone'] ?: $this->module->l('Neuvedeno', 'inquiry'),
            '{product_name}' => $data['product_name'],
            '{product_reference}' => $data['product_reference'] ?: $this->module->l('Neuvedeno', 'inquiry'),
            '{message}' => $data['message'] ?: $this->module->l('Bez zprávy', 'inquiry'),
            '{date}' => date('d.m.Y H:i', strtotime($data['date_add'])),
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{admin_url}' => $this->context->link->getAdminLink('AdminModules') . '&configure=priceinquiry'
        );

        $subject = sprintf(
            $this->module->l('Nový dotaz na cenu #%d - %s', 'inquiry'),
            $inquiryId,
            Configuration::get('PS_SHOP_NAME')
        );

        return Mail::Send(
            $this->context->language->id,
            'price_inquiry_admin',
            $subject,
            $templateVars,
            $adminEmail,
            null,
            null,
            null,
            null,
            null,
            dirname(__FILE__) . '/../../mails/',
            false,
            null,
            null
        );
    }

    /**
     * Odeslání potvrzovacího e-mailu zákazníkovi
     */
    private function sendCustomerConfirmation($inquiryId, $data)
    {
        if (!$data['customer_email'] || !Validate::isEmail($data['customer_email'])) {
            return false;
        }

        // Příprava dat pro e-mail
        $templateVars = array(
            '{inquiry_id}' => $inquiryId,
            '{customer_name}' => $data['customer_name'],
            '{product_name}' => $data['product_name'],
            '{product_reference}' => $data['product_reference'] ?: $this->module->l('Neuvedeno', 'inquiry'),
            '{message}' => $data['message'] ?: $this->module->l('Bez zprávy', 'inquiry'),
            '{date}' => date('d.m.Y H:i', strtotime($data['date_add'])),
            '{shop_name}' => Configuration::get('PS_SHOP_NAME'),
            '{shop_email}' => Configuration::get('PS_SHOP_EMAIL'),
            '{shop_url}' => $this->context->shop->getBaseURL(true)
        );

        $subject = sprintf(
            $this->module->l('Potvrzení dotazu na cenu #%d - %s', 'inquiry'),
            $inquiryId,
            Configuration::get('PS_SHOP_NAME')
        );

        return Mail::Send(
            $this->context->language->id,
            'price_inquiry_customer',
            $subject,
            $templateVars,
            $data['customer_email'],
            $data['customer_name'],
            null,
            null,
            null,
            null,
            dirname(__FILE__) . '/../../mails/',
            false,
            null,
            null
        );
    }

    /**
     * Zobrazení stránky (pro non-AJAX požadavky)
     */
    public function initContent()
    {
        // Přesměrování na hlavní stránku pro non-AJAX požadavky
        Tools::redirect('index.php');
    }
}
