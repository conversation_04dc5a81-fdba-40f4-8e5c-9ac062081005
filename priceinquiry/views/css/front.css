/**
 * Styly pro frontend modul "Cena na dotaz"
 */

/* Kontejner pro tlačítko zjistit cenu */
.price-inquiry-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.price-inquiry-wrapper {
    text-align: center;
}

/* <PERSON><PERSON><PERSON><PERSON>t<PERSON> "Zjistit cenu" */
.price-inquiry-btn {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    padding: 12px 24px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    min-width: 180px;
    justify-content: center;
}

.price-inquiry-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.price-inquiry-btn:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.price-inquiry-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Ikona v tlačítku */
.price-inquiry-btn .material-icons {
    font-size: 18px;
}

/* Informační text */
.price-inquiry-info {
    margin-top: 12px;
    margin-bottom: 0;
    color: #6c757d;
    font-size: 14px;
    line-height: 1.4;
}

/* Text "Cena na dotaz" místo ceny */
.price-inquiry-text {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    display: inline-block;
    padding: 8px 16px;
    background-color: #e7f3ff;
    border: 2px solid #007bff;
    border-radius: 6px;
    margin: 10px 0;
}

/* Skrytí původních prvků pro přidání do košíku */
.product-add-to-cart,
.add-to-cart,
.product-actions .add-to-cart {
    display: none !important;
}

/* Responsive design */
@media (max-width: 768px) {
    .price-inquiry-container {
        margin: 15px 0;
        padding: 12px;
    }
    
    .price-inquiry-btn {
        padding: 10px 20px;
        font-size: 14px;
        min-width: 160px;
    }
    
    .price-inquiry-text {
        font-size: 20px;
        padding: 6px 12px;
    }
    
    .price-inquiry-info {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .price-inquiry-btn {
        width: 100%;
        max-width: 280px;
    }
    
    .price-inquiry-text {
        font-size: 18px;
    }
}

/* ===== POPUP MODAL STYLY ===== */

/* Modal overlay */
.price-inquiry-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
}

/* Modal content */
.modal-content {
    position: relative;
    background: white;
    margin: 2% auto;
    padding: 0;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal header */
.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px 25px;
    position: relative;
    border-bottom: none;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Modal body */
.modal-body {
    padding: 25px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

/* Product info section */
.product-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 25px;
}

.product-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.product-details h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.product-reference {
    margin: 0;
    font-size: 14px;
    color: #6c757d;
}

/* Form styling */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-group label.required .required-star {
    color: #dc3545;
    margin-left: 2px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Checkbox styling */
.checkbox-group {
    margin-bottom: 25px;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    cursor: pointer;
    font-size: 14px;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    margin-top: 1px;
}

/* Messages */
.messages {
    margin-bottom: 20px;
}

.alert {
    padding: 12px 15px;
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

/* Modal footer */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    margin-top: 25px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #0056b3;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background-color: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-loading {
    display: none;
}

/* Body class when modal is open */
body.modal-open {
    overflow: hidden;
}

/* Responsive modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 90vh;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h3 {
        font-size: 18px;
    }

    .modal-body {
        padding: 20px;
    }

    .product-info {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .product-image img {
        width: 60px;
        height: 60px;
    }

    .modal-footer {
        flex-direction: column-reverse;
        gap: 10px;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 2% auto;
        border-radius: 8px;
    }

    .modal-header {
        padding: 12px 15px;
    }

    .modal-body {
        padding: 15px;
    }

    .form-control {
        padding: 8px 10px;
    }
}

/* Loading animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spin {
    animation: spin 1s linear infinite;
}
